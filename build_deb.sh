#!/bin/bash

# Script để build file .deb cho Notes App
# Sử dụng: ./build_deb.sh

set -e

echo "🚀 Bắt đầu build Notes App..."

# Kiểm tra các dependency cần thiết
echo "📦 Kiểm tra dependencies..."

if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 chưa được cài đặt!"
    echo "Chạy: sudo apt install python3"
    exit 1
fi

if ! python3 -c "import tkinter" &> /dev/null; then
    echo "❌ tkinter chưa được cài đặt!"
    echo "Chạy: sudo apt install python3-tk"
    exit 1
fi

# Cài đặt stdeb nếu chưa có
if ! python3 -c "import stdeb" &> /dev/null; then
    echo "📥 Cài đặt python3-stdeb..."
    sudo apt update
    sudo apt install -y python3-stdeb dh-python
fi

# Dọn dẹp build cũ
echo "🧹 Dọn dẹp build cũ..."
rm -rf build/
rm -rf dist/
rm -rf deb_dist/
rm -rf *.egg-info/

# Build package
echo "🔨 Build Python package..."
python3 setup.py sdist

# Build .deb package
echo "📦 Tạo file .deb..."
python3 setup.py --command-packages=stdeb.command bdist_deb

# Kiểm tra kết quả
if [ -d "deb_dist" ]; then
    echo "✅ Build thành công!"
    echo "📁 File .deb được tạo tại:"
    ls -la deb_dist/*.deb
    echo ""
    echo "🚀 Để cài đặt, chạy:"
    echo "sudo dpkg -i deb_dist/notes-app_*.deb"
    echo "sudo apt-get install -f  # Nếu có lỗi dependency"
    echo ""
    echo "🗑️ Để gỡ cài đặt, chạy:"
    echo "sudo apt remove notes-app"
else
    echo "❌ Build thất bại!"
    exit 1
fi
