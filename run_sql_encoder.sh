#!/bin/bash

echo "🔐 Starting SQL Encoder..."

# Kill any existing instances
pkill -f "python3.*sql_encoder" 2>/dev/null

# Check Python3
if ! which python3 > /dev/null 2>&1; then
    echo "❌ Python3 not installed!"
    echo "Run: sudo apt install python3"
    exit 1
fi

# Check tkinter
if ! python3 -c "import tkinter" > /dev/null 2>&1; then
    echo "❌ tkinter not installed!"
    echo "Run: sudo apt install python3-tk"
    exit 1
fi

# Check cryptography
if ! python3 -c "import cryptography" > /dev/null 2>&1; then
    echo "📦 Installing cryptography..."
    pip3 install cryptography
fi

echo "✅ Dependencies OK"
echo "🔐 Launching SQL Encoder..."

# Run the app
python3 sql_encoder.py
