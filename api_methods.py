#!/usr/bin/env python3
"""
API Methods - Các method để xử lý API calls
"""

import requests
import json
import csv
from datetime import datetime
import threading
from tkinter import messagebox, filedialog
import tkinter as tk

def call_api(self):
    """Gọi API trong thread riêng"""
    
    encoded_sql = self.api_sql_text.get("1.0", tk.END).strip()
    password = self.password_var.get().strip()
    
    if not encoded_sql:
        messagebox.showwarning("Warning", "Please enter Encoded SQL!")
        return
        
    if not password:
        messagebox.showwarning("Warning", "Please enter Password!")
        return
        
    # Start progress
    self.progress.start()
    self.status_var.set("Calling API...")
    
    # Call API in separate thread
    thread = threading.Thread(target=self._call_api_thread, args=(encoded_sql, password))
    thread.daemon = True
    thread.start()
    
def _call_api_thread(self, encoded_sql, password):
    """Gọi API trong thread"""
    
    try:
        # Prepare request data
        data = {
            "encodedSql": encoded_sql,
            "password": password
        }
        
        # Make API call
        response = requests.post(self.api_url, headers=self.headers, json=data, timeout=30)
        
        # Process response
        if response.status_code == 200:
            result = response.json()
            
            # Update UI in main thread
            self.root.after(0, self._handle_api_success, result)
        else:
            error_msg = f"API Error: {response.status_code} - {response.text}"
            self.root.after(0, self._handle_api_error, error_msg)
            
    except requests.exceptions.Timeout:
        self.root.after(0, self._handle_api_error, "Request timeout (30s)")
    except requests.exceptions.ConnectionError:
        self.root.after(0, self._handle_api_error, "Connection error")
    except Exception as e:
        self.root.after(0, self._handle_api_error, f"Error: {str(e)}")
        
def _handle_api_success(self, result):
    """Xử lý khi API thành công"""
    
    # Stop progress
    self.progress.stop()
    
    # Check API response status
    if result.get('status') == '01' and result.get('message') == 'Successful!':
        data = result.get('data', [])
        
        if data:
            self.current_data = data
            self.filtered_data = data.copy()
            self.display_data(data)
            self.status_var.set(f"Success! Retrieved {len(data)} records at {datetime.now().strftime('%H:%M:%S')}")
            
            # Enable export buttons
            self.enable_export_buttons()
        else:
            self.status_var.set("Success but no data returned")
            messagebox.showinfo("Info", "API call successful but no data returned")
    else:
        error_msg = f"API returned error: {result.get('message', 'Unknown error')}"
        self.status_var.set("API Error")
        messagebox.showerror("API Error", error_msg)
        
def _handle_api_error(self, error_msg):
    """Xử lý khi API lỗi"""
    
    # Stop progress
    self.progress.stop()
    
    self.status_var.set("API call failed")
    messagebox.showerror("Error", error_msg)
    
def enable_export_buttons(self):
    """Enable export buttons"""
    
    # Find and enable export buttons in API tab
    api_frame = self.notebook.nametowidget(self.notebook.tabs()[1])
    for widget in api_frame.winfo_children():
        if isinstance(widget, ttk.LabelFrame) and "API Input" in widget.cget('text'):
            for child in widget.winfo_children():
                if isinstance(child, ttk.Frame):
                    for button in child.winfo_children():
                        if isinstance(button, ttk.Button):
                            if "Export CSV" in button.cget('text') or "Copy Data" in button.cget('text'):
                                button.configure(state=tk.NORMAL)
                                
def display_data(self, data):
    """Hiển thị dữ liệu trong bảng"""
    
    if not data:
        return
        
    # Clear existing data
    for item in self.tree.get_children():
        self.tree.delete(item)
        
    # Get all unique columns from data
    columns = set()
    for item in data:
        if isinstance(item, dict):
            columns.update(item.keys())
    columns = sorted(list(columns))
    
    # Configure treeview columns
    self.tree["columns"] = columns
    
    # Configure column headings and widths
    for col in columns:
        self.tree.heading(col, text=col.replace('_', ' ').title())
        self.tree.column(col, width=120, minwidth=80)
        
    # Insert data
    for i, item in enumerate(data):
        if isinstance(item, dict):
            values = []
            for col in columns:
                value = item.get(col, '')
                # Handle None values and long text
                if value is None:
                    value = ''
                elif isinstance(value, str) and len(value) > 50:
                    value = value[:47] + '...'
                values.append(str(value))
            
            self.tree.insert("", "end", values=values, tags=(f"row{i % 2}",))
            
    # Configure row colors
    self.tree.tag_configure("row0", background="#ffffff")
    self.tree.tag_configure("row1", background="#f0f0f0")
    
    # Update info
    self.update_info()
    
def update_info(self):
    """Cập nhật thông tin hiển thị"""
    total = len(self.current_data)
    filtered = len(self.filtered_data)
    
    if total == filtered:
        self.info_var.set(f"Total: {total} records")
    else:
        self.info_var.set(f"Showing: {filtered} / {total} records")
        
def on_search(self, event=None):
    """Xử lý tìm kiếm"""
    
    search_term = self.search_var.get().lower().strip()
    
    if not search_term:
        self.filtered_data = self.current_data.copy()
    else:
        self.filtered_data = []
        for item in self.current_data:
            # Search in all values of the item
            item_text = ' '.join(str(v).lower() for v in item.values() if v is not None)
            if search_term in item_text:
                self.filtered_data.append(item)
                
    self.display_data(self.filtered_data)
    
def on_item_double_click(self, event):
    """Xử lý double click vào item"""
    
    selection = self.tree.selection()
    if selection:
        item = self.tree.item(selection[0])
        values = item['values']
        columns = self.tree["columns"]
        
        # Create detail window
        self.show_item_detail(dict(zip(columns, values)))
        
def show_item_detail(self, item_data):
    """Hiển thị chi tiết item"""
    
    detail_window = tk.Toplevel(self.root)
    detail_window.title("Item Detail")
    detail_window.geometry("600x400")
    detail_window.transient(self.root)
    
    # Create text widget with scrollbar
    text_frame = ttk.Frame(detail_window, padding="10")
    text_frame.pack(fill=tk.BOTH, expand=True)
    
    text_widget = tk.Text(text_frame, wrap=tk.WORD, font=("Courier", 10))
    scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=text_widget.yview)
    text_widget.configure(yscrollcommand=scrollbar.set)
    
    text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    # Insert formatted data
    for key, value in item_data.items():
        text_widget.insert(tk.END, f"{key}:\n{value}\n\n")
        
    text_widget.configure(state=tk.DISABLED)
    
def clear_api_data(self):
    """Xóa dữ liệu API"""
    
    self.api_sql_text.delete("1.0", tk.END)
    self.search_var.set("")
    
    # Clear data
    self.current_data = []
    self.filtered_data = []
    
    # Clear treeview
    for item in self.tree.get_children():
        self.tree.delete(item)
        
    self.info_var.set("No data")
    self.status_var.set("API data cleared")
    
def export_csv(self):
    """Export dữ liệu ra CSV"""
    
    if not self.filtered_data:
        messagebox.showwarning("Warning", "No data to export!")
        return
        
    # Ask for file location
    filename = filedialog.asksaveasfilename(
        defaultextension=".csv",
        filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
        title="Save CSV file"
    )
    
    if filename:
        try:
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                if self.filtered_data:
                    fieldnames = self.filtered_data[0].keys()
                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                    writer.writeheader()
                    writer.writerows(self.filtered_data)
                    
            messagebox.showinfo("Success", f"Data exported to {filename}")
            self.status_var.set(f"Exported {len(self.filtered_data)} records to CSV")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to export: {str(e)}")
            
def copy_data(self):
    """Copy dữ liệu vào clipboard"""
    
    if not self.filtered_data:
        messagebox.showwarning("Warning", "No data to copy!")
        return
        
    try:
        # Convert to tab-separated format
        if self.filtered_data:
            # Headers
            headers = list(self.filtered_data[0].keys())
            clipboard_text = '\t'.join(headers) + '\n'
            
            # Data rows
            for item in self.filtered_data:
                row = []
                for header in headers:
                    value = item.get(header, '')
                    row.append(str(value) if value is not None else '')
                clipboard_text += '\t'.join(row) + '\n'
                
            # Copy to clipboard
            self.root.clipboard_clear()
            self.root.clipboard_append(clipboard_text)
            
            messagebox.showinfo("Success", f"Copied {len(self.filtered_data)} records to clipboard")
            self.status_var.set("Data copied to clipboard")
            
    except Exception as e:
        messagebox.showerror("Error", f"Failed to copy: {str(e)}")
