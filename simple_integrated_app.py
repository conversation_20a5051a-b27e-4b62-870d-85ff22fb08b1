#!/usr/bin/env python3
"""
Simple Integrated App - SQL Encoder + API Caller (X11 safe)
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import requests
import json
import csv
from datetime import datetime
import threading
import base64
import secrets
import hashlib
import hmac

class SimpleIntegratedApp:
    def __init__(self, root):
        self.root = root
        self.root.title("SQL Encoder + API Caller")
        self.root.geometry("1200x800")
        self.root.minsize(800, 600)
        
        # API Configuration
        self.api_url = "https://api.vipomart.vn/authen/query"
        self.headers = {
            'X-Internal-Secret': 'Nv82Lx7Kq!Dfj29slPq2R5mxVfLzXp9A',
            'User-Agent': 'Apidog/1.0.0 (https://apidog.com)',
            'Content-Type': 'application/json',
            'Cookie': 'JSESSIONID=F17B9DB321617A561FEC4966FDEFDBD3; SERVERID=A'
        }
        
        # Data storage
        self.current_data = []
        self.filtered_data = []
        self.encoded_sql = ""
        
        self.create_widgets()
        
    def create_widgets(self):
        """Tạo giao diện đơn giản"""
        
        # Main container
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        title_label = ttk.Label(main_frame, text="SQL Encoder + API Caller", 
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # Create notebook for tabs
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # Tab 1: SQL Encoder
        self.create_encoder_tab()
        
        # Tab 2: API Caller
        self.create_api_tab()
        
        # Status bar
        self.status_var = tk.StringVar(value="Ready")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.pack(fill=tk.X, pady=(10, 0))
        
    def create_encoder_tab(self):
        """Tạo tab SQL Encoder"""
        
        encoder_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(encoder_frame, text="SQL Encoder")
        
        # Password section
        password_frame = ttk.LabelFrame(encoder_frame, text="Password", padding="10")
        password_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(password_frame, text="Password:").pack(side=tk.LEFT)
        self.password_var = tk.StringVar(value="test")
        self.password_entry = ttk.Entry(password_frame, textvariable=self.password_var, 
                                       show="*", width=20)
        self.password_entry.pack(side=tk.LEFT, padx=(10, 0))
        
        # Show/Hide password
        self.show_password_var = tk.BooleanVar()
        show_password_cb = ttk.Checkbutton(password_frame, text="Show", 
                                          variable=self.show_password_var,
                                          command=self.toggle_password)
        show_password_cb.pack(side=tk.LEFT, padx=(10, 0))
        
        # SQL Input section
        sql_frame = ttk.LabelFrame(encoder_frame, text="SQL Query Input", padding="10")
        sql_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # SQL text area
        self.sql_text = tk.Text(sql_frame, wrap=tk.WORD, height=8)
        sql_scrollbar = ttk.Scrollbar(sql_frame, orient=tk.VERTICAL, command=self.sql_text.yview)
        self.sql_text.configure(yscrollcommand=sql_scrollbar.set)
        
        self.sql_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        sql_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Control buttons
        control_frame = ttk.Frame(sql_frame)
        control_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(control_frame, text="Load Sample SQL", 
                  command=self.load_sample_sql).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(control_frame, text="Encrypt SQL", 
                  command=self.encrypt_sql).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(control_frame, text="Encrypt & Call API", 
                  command=self.encrypt_and_call_api).pack(side=tk.LEFT, padx=(0, 10))
        
        self.copy_encoded_btn = ttk.Button(control_frame, text="Copy Encoded", 
                                          command=self.copy_encoded, state=tk.DISABLED)
        self.copy_encoded_btn.pack(side=tk.RIGHT)
        
        # Result section
        result_frame = ttk.LabelFrame(encoder_frame, text="Encoded SQL Result", padding="10")
        result_frame.pack(fill=tk.BOTH, expand=True)
        
        # Result text area
        self.encoded_text = tk.Text(result_frame, wrap=tk.WORD, height=6, state=tk.DISABLED)
        encoded_scrollbar = ttk.Scrollbar(result_frame, orient=tk.VERTICAL, 
                                         command=self.encoded_text.yview)
        self.encoded_text.configure(yscrollcommand=encoded_scrollbar.set)
        
        self.encoded_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        encoded_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
    def create_api_tab(self):
        """Tạo tab API Caller"""
        
        api_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(api_frame, text="API Caller")
        
        # Input section
        input_frame = ttk.LabelFrame(api_frame, text="API Input", padding="10")
        input_frame.pack(fill=tk.X, pady=(0, 10))
        
        # EncodedSql input
        ttk.Label(input_frame, text="Encoded SQL:").pack(anchor=tk.W)
        
        self.api_sql_text = tk.Text(input_frame, height=3, wrap=tk.WORD)
        api_sql_scrollbar = ttk.Scrollbar(input_frame, orient=tk.VERTICAL, 
                                         command=self.api_sql_text.yview)
        self.api_sql_text.configure(yscrollcommand=api_sql_scrollbar.set)
        
        self.api_sql_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, pady=(5, 10))
        api_sql_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=(5, 10))
        
        # Control buttons
        api_control_frame = ttk.Frame(input_frame)
        api_control_frame.pack(fill=tk.X)
        
        ttk.Button(api_control_frame, text="Call API", 
                  command=self.call_api).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(api_control_frame, text="Paste from Encoder", 
                  command=self.paste_from_encoder).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(api_control_frame, text="Clear", 
                  command=self.clear_api_data).pack(side=tk.LEFT)
        
        # Export buttons
        self.export_csv_btn = ttk.Button(api_control_frame, text="Export CSV", 
                                        command=self.export_csv, state=tk.DISABLED)
        self.export_csv_btn.pack(side=tk.RIGHT, padx=(10, 0))
        
        self.copy_data_btn = ttk.Button(api_control_frame, text="Copy Data", 
                                       command=self.copy_data, state=tk.DISABLED)
        self.copy_data_btn.pack(side=tk.RIGHT)
        
        # Results section
        results_frame = ttk.LabelFrame(api_frame, text="API Results", padding="10")
        results_frame.pack(fill=tk.BOTH, expand=True)
        
        # Search bar
        search_frame = ttk.Frame(results_frame)
        search_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(search_frame, text="Search:").pack(side=tk.LEFT)
        self.search_var = tk.StringVar()
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var, width=30)
        search_entry.pack(side=tk.LEFT, padx=(10, 0))
        search_entry.bind('<KeyRelease>', self.on_search)
        
        # Results info
        self.info_var = tk.StringVar(value="No data")
        info_label = ttk.Label(search_frame, textvariable=self.info_var)
        info_label.pack(side=tk.RIGHT)
        
        # Progress bar
        self.progress = ttk.Progressbar(results_frame, mode='indeterminate')
        self.progress.pack(fill=tk.X, pady=(0, 10))
        
        # Treeview for results
        tree_frame = ttk.Frame(results_frame)
        tree_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create treeview with scrollbars
        self.tree = ttk.Treeview(tree_frame, show="headings")
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.tree.yview)
        h_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        self.tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Pack treeview and scrollbars
        self.tree.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")
        
        # Configure grid weights
        tree_frame.grid_rowconfigure(0, weight=1)
        tree_frame.grid_columnconfigure(0, weight=1)
        
        # Bind double click
        self.tree.bind("<Double-1>", self.on_item_double_click)
        
    def toggle_password(self):
        """Toggle hiển thị password"""
        if self.show_password_var.get():
            self.password_entry.configure(show='')
        else:
            self.password_entry.configure(show='*')
            
    def load_sample_sql(self):
        """Load SQL mẫu"""
        sample_sql = """SELECT 
    c.id,
    c.name,
    c.code,
    c.icon,
    c.parent_id,
    c.description,
    c.published
FROM categories c 
WHERE c.published = 1 
    AND c.parent_id IS NOT NULL
ORDER BY c.name ASC
LIMIT 10"""
        
        self.sql_text.delete("1.0", tk.END)
        self.sql_text.insert("1.0", sample_sql)
        self.status_var.set("Sample SQL loaded")
        
    def simple_encrypt(self, text, password):
        """Simple encryption (for demo - not production ready)"""
        try:
            # Simple base64 encoding with password salt
            salt = secrets.token_bytes(16)
            key = hashlib.pbkdf2_hmac('sha256', password.encode(), salt, 100000)
            
            # XOR encryption (simple demo)
            text_bytes = text.encode('utf-8')
            encrypted = bytearray()
            
            for i, byte in enumerate(text_bytes):
                encrypted.append(byte ^ key[i % len(key)])
            
            # Combine salt + encrypted
            combined = salt + bytes(encrypted)
            return base64.b64encode(combined).decode('utf-8')
            
        except Exception as e:
            raise Exception(f"Encryption failed: {str(e)}")
            
    def encrypt_sql(self):
        """Mã hóa SQL"""
        
        sql_query = self.sql_text.get("1.0", tk.END).strip()
        password = self.password_var.get().strip()
        
        if not sql_query:
            messagebox.showwarning("Warning", "Please enter SQL query!")
            return None
            
        if not password:
            messagebox.showwarning("Warning", "Please enter password!")
            return None
            
        try:
            # Encrypt SQL
            self.status_var.set("Encrypting...")
            self.root.update()
            
            self.encoded_sql = self.simple_encrypt(sql_query, password)
            
            # Display result
            self.encoded_text.configure(state=tk.NORMAL)
            self.encoded_text.delete("1.0", tk.END)
            self.encoded_text.insert("1.0", self.encoded_sql)
            self.encoded_text.configure(state=tk.DISABLED)
            
            # Enable copy button
            self.copy_encoded_btn.configure(state=tk.NORMAL)
            
            self.status_var.set(f"Encryption successful! Length: {len(self.encoded_sql)} characters")
            return self.encoded_sql
            
        except Exception as e:
            messagebox.showerror("Error", f"Encryption failed: {str(e)}")
            self.status_var.set("Encryption failed")
            return None

    def copy_encoded(self):
        """Copy encoded SQL vào clipboard"""

        if self.encoded_sql:
            self.root.clipboard_clear()
            self.root.clipboard_append(self.encoded_sql)
            messagebox.showinfo("Success", "Encoded SQL copied to clipboard!")
            self.status_var.set("Copied to clipboard")
        else:
            messagebox.showwarning("Warning", "No encoded SQL to copy!")

    def encrypt_and_call_api(self):
        """Mã hóa SQL và gọi API luôn"""

        encoded_sql = self.encrypt_sql()
        if encoded_sql:
            # Switch to API tab
            self.notebook.select(1)

            # Paste encoded SQL to API tab
            self.api_sql_text.delete("1.0", tk.END)
            self.api_sql_text.insert("1.0", encoded_sql)

            # Call API
            self.call_api()

    def paste_from_encoder(self):
        """Paste encoded SQL từ encoder tab"""

        if self.encoded_sql:
            self.api_sql_text.delete("1.0", tk.END)
            self.api_sql_text.insert("1.0", self.encoded_sql)
            self.status_var.set("Pasted from encoder")
        else:
            messagebox.showwarning("Warning", "No encoded SQL to paste! Please encrypt SQL first.")

    def call_api(self):
        """Gọi API trong thread riêng"""

        encoded_sql = self.api_sql_text.get("1.0", tk.END).strip()
        password = self.password_var.get().strip()

        if not encoded_sql:
            messagebox.showwarning("Warning", "Please enter Encoded SQL!")
            return

        if not password:
            messagebox.showwarning("Warning", "Please enter Password!")
            return

        # Start progress
        self.progress.start()
        self.status_var.set("Calling API...")

        # Call API in separate thread
        thread = threading.Thread(target=self._call_api_thread, args=(encoded_sql, password))
        thread.daemon = True
        thread.start()

    def _call_api_thread(self, encoded_sql, password):
        """Gọi API trong thread"""

        try:
            # Prepare request data
            data = {
                "encodedSql": encoded_sql,
                "password": password
            }

            # Make API call
            response = requests.post(self.api_url, headers=self.headers, json=data, timeout=30)

            # Process response
            if response.status_code == 200:
                result = response.json()

                # Update UI in main thread
                self.root.after(0, self._handle_api_success, result)
            else:
                error_msg = f"API Error: {response.status_code} - {response.text}"
                self.root.after(0, self._handle_api_error, error_msg)

        except requests.exceptions.Timeout:
            self.root.after(0, self._handle_api_error, "Request timeout (30s)")
        except requests.exceptions.ConnectionError:
            self.root.after(0, self._handle_api_error, "Connection error")
        except Exception as e:
            self.root.after(0, self._handle_api_error, f"Error: {str(e)}")

    def _handle_api_success(self, result):
        """Xử lý khi API thành công"""

        # Stop progress
        self.progress.stop()

        # Check API response status
        if result.get('status') == '01' and result.get('message') == 'Successful!':
            data = result.get('data', [])

            if data:
                self.current_data = data
                self.filtered_data = data.copy()
                self.display_data(data)
                self.status_var.set(f"Success! Retrieved {len(data)} records at {datetime.now().strftime('%H:%M:%S')}")

                # Enable export buttons
                self.export_csv_btn.configure(state=tk.NORMAL)
                self.copy_data_btn.configure(state=tk.NORMAL)
            else:
                self.status_var.set("Success but no data returned")
                messagebox.showinfo("Info", "API call successful but no data returned")
        else:
            error_msg = f"API returned error: {result.get('message', 'Unknown error')}"
            self.status_var.set("API Error")
            messagebox.showerror("API Error", error_msg)

    def _handle_api_error(self, error_msg):
        """Xử lý khi API lỗi"""

        # Stop progress
        self.progress.stop()

        self.status_var.set("API call failed")
        messagebox.showerror("Error", error_msg)

    def display_data(self, data):
        """Hiển thị dữ liệu trong bảng"""

        if not data:
            return

        # Clear existing data
        for item in self.tree.get_children():
            self.tree.delete(item)

        # Get all unique columns from data
        columns = set()
        for item in data:
            if isinstance(item, dict):
                columns.update(item.keys())
        columns = sorted(list(columns))

        # Configure treeview columns
        self.tree["columns"] = columns

        # Configure column headings and widths
        for col in columns:
            self.tree.heading(col, text=col.replace('_', ' ').title())
            self.tree.column(col, width=120, minwidth=80)

        # Insert data
        for i, item in enumerate(data):
            if isinstance(item, dict):
                values = []
                for col in columns:
                    value = item.get(col, '')
                    # Handle None values and long text
                    if value is None:
                        value = ''
                    elif isinstance(value, str) and len(value) > 50:
                        value = value[:47] + '...'
                    values.append(str(value))

                self.tree.insert("", "end", values=values, tags=(f"row{i % 2}",))

        # Configure row colors
        self.tree.tag_configure("row0", background="#ffffff")
        self.tree.tag_configure("row1", background="#f0f0f0")

        # Update info
        self.update_info()

    def update_info(self):
        """Cập nhật thông tin hiển thị"""
        total = len(self.current_data)
        filtered = len(self.filtered_data)

        if total == filtered:
            self.info_var.set(f"Total: {total} records")
        else:
            self.info_var.set(f"Showing: {filtered} / {total} records")

    def on_search(self, event=None):
        """Xử lý tìm kiếm"""

        search_term = self.search_var.get().lower().strip()

        if not search_term:
            self.filtered_data = self.current_data.copy()
        else:
            self.filtered_data = []
            for item in self.current_data:
                # Search in all values of the item
                item_text = ' '.join(str(v).lower() for v in item.values() if v is not None)
                if search_term in item_text:
                    self.filtered_data.append(item)

        self.display_data(self.filtered_data)

    def on_item_double_click(self, event):
        """Xử lý double click vào item"""

        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            values = item['values']
            columns = self.tree["columns"]

            # Create detail window
            self.show_item_detail(dict(zip(columns, values)))

    def show_item_detail(self, item_data):
        """Hiển thị chi tiết item"""

        detail_window = tk.Toplevel(self.root)
        detail_window.title("Item Detail")
        detail_window.geometry("600x400")
        detail_window.transient(self.root)

        # Create text widget with scrollbar
        text_frame = ttk.Frame(detail_window, padding="10")
        text_frame.pack(fill=tk.BOTH, expand=True)

        text_widget = tk.Text(text_frame, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)

        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Insert formatted data
        for key, value in item_data.items():
            text_widget.insert(tk.END, f"{key}:\n{value}\n\n")

        text_widget.configure(state=tk.DISABLED)

    def clear_api_data(self):
        """Xóa dữ liệu API"""

        self.api_sql_text.delete("1.0", tk.END)
        self.search_var.set("")

        # Clear data
        self.current_data = []
        self.filtered_data = []

        # Clear treeview
        for item in self.tree.get_children():
            self.tree.delete(item)

        self.info_var.set("No data")
        self.status_var.set("API data cleared")

    def export_csv(self):
        """Export dữ liệu ra CSV"""

        if not self.filtered_data:
            messagebox.showwarning("Warning", "No data to export!")
            return

        # Ask for file location
        filename = filedialog.asksaveasfilename(
            defaultextension=".csv",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
            title="Save CSV file"
        )

        if filename:
            try:
                with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                    if self.filtered_data:
                        fieldnames = self.filtered_data[0].keys()
                        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                        writer.writeheader()
                        writer.writerows(self.filtered_data)

                messagebox.showinfo("Success", f"Data exported to {filename}")
                self.status_var.set(f"Exported {len(self.filtered_data)} records to CSV")

            except Exception as e:
                messagebox.showerror("Error", f"Failed to export: {str(e)}")

    def copy_data(self):
        """Copy dữ liệu vào clipboard"""

        if not self.filtered_data:
            messagebox.showwarning("Warning", "No data to copy!")
            return

        try:
            # Convert to tab-separated format
            if self.filtered_data:
                # Headers
                headers = list(self.filtered_data[0].keys())
                clipboard_text = '\t'.join(headers) + '\n'

                # Data rows
                for item in self.filtered_data:
                    row = []
                    for header in headers:
                        value = item.get(header, '')
                        row.append(str(value) if value is not None else '')
                    clipboard_text += '\t'.join(row) + '\n'

                # Copy to clipboard
                self.root.clipboard_clear()
                self.root.clipboard_append(clipboard_text)

                messagebox.showinfo("Success", f"Copied {len(self.filtered_data)} records to clipboard")
                self.status_var.set("Data copied to clipboard")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to copy: {str(e)}")


def main():
    """Main function"""
    root = tk.Tk()
    app = SimpleIntegratedApp(root)

    def on_closing():
        if messagebox.askokcancel("Exit", "Exit application?"):
            root.destroy()

    root.protocol("WM_DELETE_WINDOW", on_closing)
    root.mainloop()


if __name__ == "__main__":
    main()
