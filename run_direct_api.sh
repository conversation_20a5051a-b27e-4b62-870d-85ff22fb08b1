#!/bin/bash

echo "🔗 Starting Direct API Caller..."

# Kill any existing instances
pkill -f "python3.*direct_api" 2>/dev/null
pkill -f "python3.*integrated" 2>/dev/null

# Check Python3
if ! which python3 > /dev/null 2>&1; then
    echo "❌ Python3 not installed!"
    echo "Run: sudo apt install python3"
    exit 1
fi

# Check tkinter
if ! python3 -c "import tkinter" > /dev/null 2>&1; then
    echo "❌ tkinter not installed!"
    echo "Run: sudo apt install python3-tk"
    exit 1
fi

# Check and install dependencies
echo "📦 Checking dependencies..."

if ! python3 -c "import requests" > /dev/null 2>&1; then
    echo "📦 Installing requests..."
    pip3 install requests
fi

echo "✅ Dependencies OK"
echo "🚀 Launching Direct API Caller..."
echo ""
echo "💡 Usage:"
echo "   1. Enter your encoded SQL in the text area"
echo "   2. Set password (default: 'test')"
echo "   3. Click 'Call API' or press Ctrl+Enter"
echo "   4. View results in the table below"
echo ""
echo "⌨️  Keyboard shortcuts:"
echo "   Ctrl+Enter: Call API"
echo "   Ctrl+V: Paste from clipboard"
echo "   Ctrl+L: Clear input"
echo ""

# Run the app
python3 direct_api_caller.py
