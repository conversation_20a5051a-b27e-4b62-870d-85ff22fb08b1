#!/usr/bin/env python3
"""
Minimal API GUI - Giao diện tối giản để tránh lỗi X11
"""

import tkinter as tk
from tkinter import messagebox, filedialog
import requests
import json
import csv
from datetime import datetime
import threading

class Minimal<PERSON>IGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("API Caller")
        self.root.geometry("900x600")
        
        # API Configuration
        self.api_url = "https://api.vipomart.vn/authen/query"
        self.headers = {
            'X-Internal-Secret': 'Nv82Lx7Kq!Dfj29slPq2R5mxVfLzXp9A',
            'User-Agent': 'Apidog/1.0.0 (https://apidog.com)',
            'Content-Type': 'application/json',
            'Cookie': 'JSESSIONID=F17B9DB321617A561FEC4966FDEFDBD3; SERVERID=A'
        }
        
        self.current_data = []
        self.create_widgets()
        
    def create_widgets(self):
        """Tạo giao diện tối giản"""
        
        # Title
        title_label = tk.Label(self.root, text="API Caller - Vipomart", font=("Arial", 16))
        title_label.pack(pady=10)
        
        # Password frame
        password_frame = tk.Frame(self.root)
        password_frame.pack(pady=5)
        
        tk.Label(password_frame, text="Password:").pack(side=tk.LEFT)
        self.password_var = tk.StringVar(value="test")
        self.password_entry = tk.Entry(password_frame, textvariable=self.password_var, show="*", width=15)
        self.password_entry.pack(side=tk.LEFT, padx=5)
        
        # Show password checkbox
        self.show_password_var = tk.BooleanVar()
        show_cb = tk.Checkbutton(password_frame, text="Show", variable=self.show_password_var, 
                                command=self.toggle_password)
        show_cb.pack(side=tk.LEFT, padx=5)
        
        # Encoded SQL input
        tk.Label(self.root, text="Encoded SQL:", font=("Arial", 12)).pack(pady=(20, 5))
        
        # Text area for encoded SQL
        self.sql_text = tk.Text(self.root, height=6, width=80)
        self.sql_text.pack(pady=5, padx=20, fill=tk.X)
        
        # Buttons frame
        button_frame = tk.Frame(self.root)
        button_frame.pack(pady=10)
        
        # Main buttons
        tk.Button(button_frame, text="Call API", command=self.call_api, 
                 bg="green", fg="white", font=("Arial", 12)).pack(side=tk.LEFT, padx=5)
        
        tk.Button(button_frame, text="Load Sample", command=self.load_sample).pack(side=tk.LEFT, padx=5)
        
        tk.Button(button_frame, text="Clear", command=self.clear_all).pack(side=tk.LEFT, padx=5)
        
        # Export buttons
        self.export_csv_btn = tk.Button(button_frame, text="Export CSV", command=self.export_csv, state=tk.DISABLED)
        self.export_csv_btn.pack(side=tk.RIGHT, padx=5)
        
        # Results area
        tk.Label(self.root, text="Results:", font=("Arial", 12)).pack(pady=(20, 5))
        
        # Results text area with scrollbar
        result_frame = tk.Frame(self.root)
        result_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=5)
        
        self.result_text = tk.Text(result_frame, height=15, width=80)
        scrollbar = tk.Scrollbar(result_frame, orient=tk.VERTICAL, command=self.result_text.yview)
        self.result_text.configure(yscrollcommand=scrollbar.set)
        
        self.result_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Status bar
        self.status_var = tk.StringVar(value="Ready")
        status_bar = tk.Label(self.root, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.pack(fill=tk.X, pady=5)
        
    def toggle_password(self):
        """Toggle password visibility"""
        if self.show_password_var.get():
            self.password_entry.configure(show='')
        else:
            self.password_entry.configure(show='*')
            
    def load_sample(self):
        """Load sample encoded SQL"""
        sample = "dGVzdF9lbmNyeXB0ZWRfc3FsX2RhdGFfaGVyZS4uLlNFTEVDVCBjLmlkLCBjLm5hbWUsIGMuY29kZSwgYy5pY29uLCBjLnBhcmVudF9pZCwgYy5kZXNjcmlwdGlvbiwgYy5wdWJsaXNoZWQgRlJPTSBjYXRlZ29yaWVzIGMgV0hFUkUgYy5wdWJsaXNoZWQgPSAxIEFORCBjLnBhcmVudF9pZCBJUyBOT1QgTlVMTCBPUkRFUiBCWSBjLm5hbWUgQVNDIExJTUlUIDEw"
        
        self.sql_text.delete("1.0", tk.END)
        self.sql_text.insert("1.0", sample)
        self.status_var.set("Sample loaded")
        
    def clear_all(self):
        """Clear all inputs and results"""
        self.sql_text.delete("1.0", tk.END)
        self.result_text.delete("1.0", tk.END)
        self.current_data = []
        self.export_csv_btn.configure(state=tk.DISABLED)
        self.status_var.set("Cleared")
        
    def call_api(self):
        """Call API"""
        encoded_sql = self.sql_text.get("1.0", tk.END).strip()
        password = self.password_var.get().strip()
        
        if not encoded_sql:
            messagebox.showwarning("Warning", "Please enter encoded SQL!")
            return
            
        if not password:
            messagebox.showwarning("Warning", "Please enter password!")
            return
            
        self.status_var.set("Calling API...")
        self.root.update()
        
        # Call API in thread
        thread = threading.Thread(target=self._call_api_thread, args=(encoded_sql, password))
        thread.daemon = True
        thread.start()
        
    def _call_api_thread(self, encoded_sql, password):
        """Call API in thread"""
        try:
            data = {
                "encodedSql": encoded_sql,
                "password": password
            }
            
            response = requests.post(self.api_url, headers=self.headers, json=data, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                self.root.after(0, self._handle_success, result)
            else:
                error_msg = f"API Error: {response.status_code} - {response.text}"
                self.root.after(0, self._handle_error, error_msg)
                
        except requests.exceptions.Timeout:
            self.root.after(0, self._handle_error, "Request timeout (30s)")
        except requests.exceptions.ConnectionError:
            self.root.after(0, self._handle_error, "Connection error")
        except Exception as e:
            self.root.after(0, self._handle_error, f"Error: {str(e)}")
            
    def _handle_success(self, result):
        """Handle API success"""
        if result.get('status') == '01' and result.get('message') == 'Successful!':
            data = result.get('data', [])
            
            if data:
                self.current_data = data
                self.display_results(data)
                self.export_csv_btn.configure(state=tk.NORMAL)
                self.status_var.set(f"Success! Retrieved {len(data)} records")
                messagebox.showinfo("Success", f"Retrieved {len(data)} records!")
            else:
                self.status_var.set("No data returned")
                messagebox.showinfo("Info", "API call successful but no data returned")
        else:
            error_msg = f"API error: {result.get('message', 'Unknown error')}"
            self.status_var.set("API Error")
            messagebox.showerror("API Error", error_msg)
            
    def _handle_error(self, error_msg):
        """Handle API error"""
        self.status_var.set("API call failed")
        messagebox.showerror("Error", error_msg)
        
    def display_results(self, data):
        """Display results in text area"""
        self.result_text.delete("1.0", tk.END)
        
        if not data:
            self.result_text.insert(tk.END, "No data to display")
            return
            
        # Display summary
        self.result_text.insert(tk.END, f"=== RESULTS ({len(data)} records) ===\n\n")
        
        # Get columns
        columns = set()
        for item in data:
            if isinstance(item, dict):
                columns.update(item.keys())
        columns = sorted(list(columns))
        
        # Display header
        header = " | ".join(f"{col[:12]:12}" for col in columns)
        self.result_text.insert(tk.END, header + "\n")
        self.result_text.insert(tk.END, "-" * len(header) + "\n")
        
        # Display data rows
        for i, item in enumerate(data):
            if isinstance(item, dict):
                row_values = []
                for col in columns:
                    value = item.get(col, '')
                    if value is None:
                        value = ''
                    value_str = str(value)
                    if len(value_str) > 12:
                        value_str = value_str[:9] + '...'
                    row_values.append(f"{value_str:12}")
                
                row = " | ".join(row_values)
                self.result_text.insert(tk.END, f"{i+1:2}. {row}\n")
                
        self.result_text.insert(tk.END, f"\nTotal: {len(data)} records\n")
        
        # Show detailed view of first record
        if data:
            self.result_text.insert(tk.END, f"\n=== FIRST RECORD DETAILS ===\n")
            first_record = data[0]
            for key, value in first_record.items():
                self.result_text.insert(tk.END, f"{key}: {value}\n")
                
    def export_csv(self):
        """Export to CSV"""
        if not self.current_data:
            messagebox.showwarning("Warning", "No data to export!")
            return
            
        filename = filedialog.asksaveasfilename(
            defaultextension=".csv",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
            title="Save CSV file",
            initialname=f"vipomart_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        )
        
        if filename:
            try:
                with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                    fieldnames = self.current_data[0].keys()
                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                    writer.writeheader()
                    writer.writerows(self.current_data)
                    
                messagebox.showinfo("Success", f"Data exported to {filename}")
                self.status_var.set(f"Exported {len(self.current_data)} records")
                
            except Exception as e:
                messagebox.showerror("Error", f"Failed to export: {str(e)}")


def main():
    """Main function"""
    root = tk.Tk()
    
    # Set basic style to avoid X11 issues
    root.configure(bg='white')
    
    app = MinimalAPIGUI(root)
    
    def on_closing():
        if messagebox.askokcancel("Exit", "Exit application?"):
            root.destroy()
    
    root.protocol("WM_DELETE_WINDOW", on_closing)
    
    # Keyboard shortcuts
    root.bind('<Control-Return>', lambda e: app.call_api())
    root.bind('<F5>', lambda e: app.call_api())
    
    root.mainloop()


if __name__ == "__main__":
    main()
