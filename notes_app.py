#!/usr/bin/env python3
"""
Ứng dụng ghi chú với giao diện dạng bảng
<PERSON>: Assistant
<PERSON><PERSON><PERSON> bản: 1.0
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import json
import os
from datetime import datetime
import sys

class NotesApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Ứng dụng Ghi chú - Notes App")
        self.root.geometry("800x600")
        self.root.minsize(600, 400)
        
        # Đường dẫn file lưu trữ
        self.data_file = os.path.expanduser("~/.notes_app_data.json")
        
        # D<PERSON> liệu ghi chú
        self.notes = []
        
        # Tạo giao diện
        self.create_widgets()
        
        # Tải dữ liệu
        self.load_data()
        
        # Cập nhật bảng
        self.refresh_table()

        # Khởi tạo panel chi tiết
        self.clear_detail_panel()
        
    def create_widgets(self):
        """Tạo c<PERSON> widget cho giao diện"""
        
        # Frame chính
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Cấu hình grid
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=2)  # Cột bảng (rộng hơn)
        main_frame.columnconfigure(1, weight=1)  # Cột chi tiết
        main_frame.rowconfigure(1, weight=1)
        
        # Tiêu đề (không dùng emoji để tránh lỗi font)
        title_label = ttk.Label(main_frame, text="UNG DUNG GHI CHU",
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, pady=(0, 10))
        
        # Frame cho bảng (bên trái)
        table_frame = ttk.Frame(main_frame)
        table_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 5))
        table_frame.columnconfigure(0, weight=1)
        table_frame.rowconfigure(0, weight=1)
        
        # Tạo Treeview (bảng)
        columns = ("ID", "Tiêu đề", "Nội dung", "Ngày tạo", "Ngày sửa")
        self.tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)
        
        # Cấu hình cột
        self.tree.heading("ID", text="ID")
        self.tree.heading("Tiêu đề", text="Tiêu đề")
        self.tree.heading("Nội dung", text="Nội dung")
        self.tree.heading("Ngày tạo", text="Ngày tạo")
        self.tree.heading("Ngày sửa", text="Ngày sửa")
        
        # Cấu hình độ rộng cột
        self.tree.column("ID", width=50, minwidth=50)
        self.tree.column("Tiêu đề", width=150, minwidth=100)
        self.tree.column("Nội dung", width=300, minwidth=200)
        self.tree.column("Ngày tạo", width=120, minwidth=100)
        self.tree.column("Ngày sửa", width=120, minwidth=100)
        
        # Scrollbar cho bảng
        scrollbar_y = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.tree.yview)
        scrollbar_x = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        self.tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)
        
        # Đặt bảng và scrollbar
        self.tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar_y.grid(row=0, column=1, sticky=(tk.N, tk.S))
        scrollbar_x.grid(row=1, column=0, sticky=(tk.W, tk.E))

        # Frame cho chi tiết ghi chú (bên phải)
        detail_frame = ttk.LabelFrame(main_frame, text="Chi tiet ghi chu", padding="10")
        detail_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(5, 0))
        detail_frame.columnconfigure(0, weight=1)
        detail_frame.rowconfigure(2, weight=1)

        # Tiêu đề ghi chú
        ttk.Label(detail_frame, text="Tieu de:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        self.detail_title = ttk.Label(detail_frame, text="", font=("Arial", 12, "bold"),
                                     foreground="blue", wraplength=250)
        self.detail_title.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        # Nội dung ghi chú
        ttk.Label(detail_frame, text="Noi dung:").grid(row=2, column=0, sticky=(tk.W, tk.N), pady=(0, 5))

        # Text widget để hiển thị nội dung
        detail_text_frame = ttk.Frame(detail_frame)
        detail_text_frame.grid(row=3, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        detail_text_frame.columnconfigure(0, weight=1)
        detail_text_frame.rowconfigure(0, weight=1)

        self.detail_content = tk.Text(detail_text_frame, wrap=tk.WORD, height=10,
                                     state=tk.DISABLED, font=("Arial", 10))
        detail_scrollbar = ttk.Scrollbar(detail_text_frame, orient=tk.VERTICAL,
                                        command=self.detail_content.yview)
        self.detail_content.configure(yscrollcommand=detail_scrollbar.set)

        self.detail_content.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        detail_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # Thông tin ngày tháng
        self.detail_dates = ttk.Label(detail_frame, text="", font=("Arial", 9),
                                     foreground="gray")
        self.detail_dates.grid(row=4, column=0, sticky=tk.W)
        
        # Frame cho các nút (span 2 cột)
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=2, column=0, columnspan=2, pady=(10, 0), sticky=(tk.W, tk.E))
        
        # Các nút chức năng (không dùng emoji)
        ttk.Button(button_frame, text="+ Them ghi chu",
                  command=self.add_note).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="Sua ghi chu",
                  command=self.edit_note).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Xoa ghi chu",
                  command=self.delete_note).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Lam moi",
                  command=self.refresh_table).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Thoat",
                  command=self.root.quit).pack(side=tk.RIGHT)
        
        # Bind events
        self.tree.bind("<Double-1>", lambda e: self.edit_note())  # Double click để sửa
        self.tree.bind("<<TreeviewSelect>>", self.on_select_note)  # Single click để hiển thị chi tiết
        
    def load_data(self):
        """Tải dữ liệu từ file JSON"""
        try:
            if os.path.exists(self.data_file):
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    self.notes = json.load(f)
        except Exception as e:
            messagebox.showerror("Lỗi", f"Không thể tải dữ liệu: {str(e)}")
            self.notes = []
    
    def save_data(self):
        """Lưu dữ liệu vào file JSON"""
        try:
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(self.notes, f, ensure_ascii=False, indent=2)
        except Exception as e:
            messagebox.showerror("Lỗi", f"Không thể lưu dữ liệu: {str(e)}")
    
    def refresh_table(self):
        """Cập nhật bảng hiển thị"""
        # Xóa tất cả dữ liệu cũ
        for item in self.tree.get_children():
            self.tree.delete(item)

        # Thêm dữ liệu mới
        for i, note in enumerate(self.notes, 1):
            # Cắt ngắn nội dung nếu quá dài
            content_preview = note['content'][:50] + "..." if len(note['content']) > 50 else note['content']

            self.tree.insert("", "end", values=(
                i,
                note['title'],
                content_preview,
                note['created_date'],
                note['modified_date']
            ))

        # Xóa panel chi tiết
        self.clear_detail_panel()
    
    def get_current_time(self):
        """Lấy thời gian hiện tại"""
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    def on_select_note(self, event):
        """Xử lý khi chọn một ghi chú trong bảng"""
        selection = self.tree.selection()
        if not selection:
            # Xóa chi tiết nếu không có ghi chú nào được chọn
            self.clear_detail_panel()
            return

        # Lấy thông tin ghi chú được chọn
        item = self.tree.item(selection[0])
        note_id = int(item['values'][0]) - 1

        if 0 <= note_id < len(self.notes):
            note = self.notes[note_id]
            self.display_note_detail(note)

    def display_note_detail(self, note):
        """Hiển thị chi tiết ghi chú trong panel bên phải"""
        # Hiển thị tiêu đề
        self.detail_title.config(text=note['title'])

        # Hiển thị nội dung
        self.detail_content.config(state=tk.NORMAL)
        self.detail_content.delete("1.0", tk.END)
        self.detail_content.insert("1.0", note['content'])
        self.detail_content.config(state=tk.DISABLED)

        # Hiển thị thông tin ngày tháng
        date_info = f"Tao: {note['created_date']}\nSua: {note['modified_date']}"
        self.detail_dates.config(text=date_info)

    def clear_detail_panel(self):
        """Xóa nội dung panel chi tiết"""
        self.detail_title.config(text="Chon mot ghi chu de xem chi tiet")
        self.detail_content.config(state=tk.NORMAL)
        self.detail_content.delete("1.0", tk.END)
        self.detail_content.insert("1.0", "Khong co ghi chu nao duoc chon.")
        self.detail_content.config(state=tk.DISABLED)
        self.detail_dates.config(text="")
    
    def add_note(self):
        """Thêm ghi chú mới"""
        dialog = NoteDialog(self.root, "Thêm ghi chú mới")
        if dialog.result:
            title, content = dialog.result
            new_note = {
                'title': title,
                'content': content,
                'created_date': self.get_current_time(),
                'modified_date': self.get_current_time()
            }
            self.notes.append(new_note)
            self.save_data()
            self.refresh_table()
            messagebox.showinfo("Thành công", "Đã thêm ghi chú mới!")
    
    def edit_note(self):
        """Sửa ghi chú đã chọn"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("Cảnh báo", "Vui lòng chọn một ghi chú để sửa!")
            return
        
        # Lấy index của ghi chú được chọn
        item = self.tree.item(selection[0])
        note_id = int(item['values'][0]) - 1
        
        current_note = self.notes[note_id]
        dialog = NoteDialog(self.root, "Sửa ghi chú", 
                           current_note['title'], current_note['content'])
        
        if dialog.result:
            title, content = dialog.result
            self.notes[note_id]['title'] = title
            self.notes[note_id]['content'] = content
            self.notes[note_id]['modified_date'] = self.get_current_time()
            self.save_data()
            self.refresh_table()
            messagebox.showinfo("Thành công", "Đã cập nhật ghi chú!")
    
    def delete_note(self):
        """Xóa ghi chú đã chọn"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("Cảnh báo", "Vui lòng chọn một ghi chú để xóa!")
            return
        
        # Xác nhận xóa
        if messagebox.askyesno("Xác nhận", "Bạn có chắc chắn muốn xóa ghi chú này?"):
            # Lấy index của ghi chú được chọn
            item = self.tree.item(selection[0])
            note_id = int(item['values'][0]) - 1
            
            del self.notes[note_id]
            self.save_data()
            self.refresh_table()
            messagebox.showinfo("Thành công", "Đã xóa ghi chú!")


class NoteDialog:
    def __init__(self, parent, title, initial_title="", initial_content=""):
        self.result = None
        
        # Tạo cửa sổ dialog
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("500x400")
        self.dialog.resizable(True, True)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # Căn giữa cửa sổ
        self.dialog.geometry("+%d+%d" % (parent.winfo_rootx() + 50, parent.winfo_rooty() + 50))
        
        # Frame chính
        main_frame = ttk.Frame(self.dialog, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Tiêu đề
        ttk.Label(main_frame, text="Tiêu đề:").pack(anchor=tk.W)
        self.title_entry = ttk.Entry(main_frame, width=50)
        self.title_entry.pack(fill=tk.X, pady=(0, 10))
        self.title_entry.insert(0, initial_title)
        
        # Nội dung
        ttk.Label(main_frame, text="Nội dung:").pack(anchor=tk.W)
        
        # Frame cho text area và scrollbar
        text_frame = ttk.Frame(main_frame)
        text_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        self.content_text = tk.Text(text_frame, wrap=tk.WORD, height=15)
        scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.content_text.yview)
        self.content_text.configure(yscrollcommand=scrollbar.set)
        
        self.content_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.content_text.insert("1.0", initial_content)
        
        # Frame cho các nút
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)
        
        ttk.Button(button_frame, text="Lưu", command=self.save).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(button_frame, text="Hủy", command=self.cancel).pack(side=tk.RIGHT)
        
        # Focus vào tiêu đề
        self.title_entry.focus()
        
        # Bind Enter để lưu (chỉ khi focus ở title)
        self.title_entry.bind("<Return>", lambda e: self.content_text.focus())
        
        # Chờ dialog đóng
        self.dialog.wait_window()
    
    def save(self):
        title = self.title_entry.get().strip()
        content = self.content_text.get("1.0", tk.END).strip()
        
        if not title:
            messagebox.showwarning("Cảnh báo", "Vui lòng nhập tiêu đề!")
            return
        
        if not content:
            messagebox.showwarning("Cảnh báo", "Vui lòng nhập nội dung!")
            return
        
        self.result = (title, content)
        self.dialog.destroy()
    
    def cancel(self):
        self.dialog.destroy()


def main():
    """Hàm main để chạy ứng dụng"""
    root = tk.Tk()
    app = NotesApp(root)
    
    # Xử lý sự kiện đóng cửa sổ
    def on_closing():
        if messagebox.askokcancel("Thoát", "Bạn có muốn thoát ứng dụng?"):
            root.destroy()
    
    root.protocol("WM_DELETE_WINDOW", on_closing)
    
    # Chạy ứng dụng
    root.mainloop()


if __name__ == "__main__":
    main()
