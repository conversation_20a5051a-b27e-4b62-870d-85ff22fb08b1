# 🔗 API Call App - Vipomart Query

Ứng dụng gọi API Vipomart và hiển thị dữ liệu dạng bảng với đầy đủ tính năng quản lý và xuất dữ liệu.

## ✨ Tính năng chính

### 🔌 API Integration
- **Gọi API Vipomart** với đúng headers và authentication
- **Input fields** cho `encodedSql` và `password`
- **Threading** để tránh đơ giao diện khi gọi API
- **Timeout handling** (30 giây)
- **Error handling** cho các trường hợp lỗi

### 📊 Hiển thị dữ liệu
- **Bảng động** tự động tạo cột theo dữ liệu trả về
- **Scrollbars** ngang và dọc
- **Row colors** xen kẽ để dễ đọc
- **Column resizing** và sorting
- **Double-click** để xem chi tiết item

### 🔍 Tìm kiếm và Filter
- **Real-time search** trong tất cả dữ liệu
- **Case-insensitive** search
- **Highlight** số lượng kết quả
- **Filter counter** hiển thị x/total records

### 📤 Export và Copy
- **Export CSV** với dialog chọn file
- **Copy to clipboard** format tab-separated
- **UTF-8 encoding** hỗ trợ tiếng Việt
- **Batch operations** trên filtered data

### 🎛️ UI/UX Features
- **Loading indicator** với progress bar
- **Status bar** hiển thị trạng thái real-time
- **Password toggle** show/hide
- **Responsive layout** với grid system
- **Error dialogs** thông báo lỗi rõ ràng

## 🚀 Cách sử dụng

### Khởi động ứng dụng
```bash
# Cách 1: Script tự động
./run_api_app.sh

# Cách 2: Chạy trực tiếp
python3 api_call_app.py
```

### Workflow cơ bản

1. **Nhập dữ liệu**:
   - Paste `encodedSql` vào text area
   - Nhập `password` (mặc định: "test")
   - Check "Show" để hiển thị password

2. **Gọi API**:
   - Click "Call API"
   - Chờ progress bar (tối đa 30s)
   - Xem kết quả trong bảng

3. **Xem và tìm kiếm**:
   - Scroll để xem tất cả columns
   - Gõ từ khóa vào Search box
   - Double-click row để xem chi tiết

4. **Export dữ liệu**:
   - Click "Export CSV" để lưu file
   - Click "Copy Data" để copy vào clipboard
   - Paste vào Excel/Google Sheets

## 📋 API Configuration

### Endpoint
```
POST https://api.vipomart.vn/authen/query
```

### Headers
```
X-Internal-Secret: Nv82Lx7Kq!Dfj29slPq2R5mxVfLzXp9A
User-Agent: Apidog/1.0.0 (https://apidog.com)
Content-Type: application/json
Cookie: [Multiple JSESSIONID values]
```

### Request Body
```json
{
    "encodedSql": "user_input_here",
    "password": "test"
}
```

### Response Format
```json
{
    "status": "01",
    "message": "Successful!",
    "data": [
        {
            "id": 1625441,
            "name": "Cháo gói, cháo tươi",
            "code": "MMCP_chaog",
            "icon": "https://...",
            "parent_id": 1625378,
            // ... other fields
        }
    ]
}
```

## 🎨 Giao diện

```
┌─────────────────────────────────────────────────────────────┐
│                API Call App - Vipomart Query                │
├─────────────────────────────────────────────────────────────┤
│ Query Input                                                 │
│ Encoded SQL: ┌─────────────────────────────────────────────┐ │
│              │ +wPz+LN4ehaLkjVbd7HxwjZ+NwLFsN3cgfLKEhzuu... │ │
│              └─────────────────────────────────────────────┘ │
│ Password: [test****] ☐ Show                                 │
├─────────────────────────────────────────────────────────────┤
│ [Call API] [Clear]                    [Export CSV] [Copy]   │
├─────────────────────────────────────────────────────────────┤
│ Results                                                     │
│ Search: [_____________]                    Total: 1 records │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ ID    │ Name              │ Code        │ Icon          │ │
│ │ 1625441│ Cháo gói, cháo... │ MMCP_chaog  │ https://...   │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ Status: Success! Retrieved 1 records at 10:30:45           │
│ ████████████████████████████████████████████████████████    │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 Dependencies

### System Requirements
- **OS**: Ubuntu 18.04+ (hoặc Linux distros khác)
- **Python**: 3.6+
- **Internet**: Để gọi API

### Python Packages
```bash
# Cài đặt tự động qua script
pip3 install requests

# Hoặc cài thủ công
sudo apt install python3-tk python3-pip
pip3 install requests
```

## 🐛 Troubleshooting

### Lỗi "requests not found"
```bash
pip3 install requests
```

### Lỗi "Connection error"
- Kiểm tra internet connection
- Kiểm tra firewall/proxy settings
- Thử với VPN nếu cần

### Lỗi "API Error: 401/403"
- Kiểm tra headers authentication
- Kiểm tra Cookie values
- Liên hệ admin để cập nhật credentials

### Lỗi "Request timeout"
- Kiểm tra network stability
- Thử lại với encodedSql ngắn hơn
- Tăng timeout trong code nếu cần

## 📊 Data Handling

### Supported Data Types
- **String**: Hiển thị bình thường, cắt ngắn nếu quá dài
- **Number**: Convert to string để hiển thị
- **null**: Hiển thị rỗng
- **Boolean**: Convert to "True"/"False"
- **Array/Object**: Convert to JSON string

### Export Formats
- **CSV**: UTF-8, comma-separated
- **Clipboard**: Tab-separated (Excel-friendly)

## 🚧 Tính năng có thể mở rộng

- [ ] **Multiple APIs**: Hỗ trợ nhiều endpoint khác nhau
- [ ] **Query Builder**: GUI để tạo SQL query
- [ ] **Data Visualization**: Charts và graphs
- [ ] **History**: Lưu lại các query đã thực hiện
- [ ] **Batch Processing**: Gọi nhiều query cùng lúc
- [ ] **Custom Headers**: Cho phép sửa headers
- [ ] **Response Caching**: Cache kết quả để tái sử dụng

## 📄 License

MIT License - Sử dụng tự do cho mục đích cá nhân và thương mại.

---

**Happy API calling! 🚀**
