#!/bin/bash

echo "🚀 Starting API Call App..."

# Kill any existing instances
pkill -f "python3.*api_call_app" 2>/dev/null

# Check Python3
if ! which python3 > /dev/null 2>&1; then
    echo "❌ Python3 not installed!"
    echo "Run: sudo apt install python3"
    exit 1
fi

# Check tkinter
if ! python3 -c "import tkinter" > /dev/null 2>&1; then
    echo "❌ tkinter not installed!"
    echo "Run: sudo apt install python3-tk"
    exit 1
fi

# Check requests
if ! python3 -c "import requests" > /dev/null 2>&1; then
    echo "📦 Installing requests..."
    pip3 install requests
fi

echo "✅ Dependencies OK"
echo "🔗 Launching API Call App..."

# Run the app
python3 api_call_app.py
