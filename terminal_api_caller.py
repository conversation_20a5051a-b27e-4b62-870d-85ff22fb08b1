#!/usr/bin/env python3
"""
Terminal API Caller - Gọi API Vipomart từ terminal
"""

import requests
import json
import csv
import sys
from datetime import datetime
import os

class TerminalAPICaller:
    def __init__(self):
        # API Configuration
        self.api_url = "https://api.vipomart.vn/authen/query"
        self.headers = {
            'X-Internal-Secret': 'Nv82Lx7Kq!Dfj29slPq2R5mxVfLzXp9A',
            'User-Agent': 'Apidog/1.0.0 (https://apidog.com)',
            'Content-Type': 'application/json',
            'Cookie': 'JSESSIONID=F17B9DB321617A561FEC4966FDEFDBD3; SERVERID=A; JSESSIONID=D61C911FBB86F7CCF49CEF37540D0877; JSESSIONID=AA2727DFD8B4C62D662DA0CEEE4513D6; JSESSIONID=E99659309A054438C6D8C3D0DFC5848B'
        }
        
    def print_banner(self):
        """In banner"""
        print("=" * 60)
        print("🔗 TERMINAL API CALLER - VIPOMART")
        print("=" * 60)
        print()
        
    def get_sample_encoded_sql(self):
        """Trả về sample encoded SQL"""
        return "dGVzdF9lbmNyeXB0ZWRfc3FsX2RhdGFfaGVyZS4uLlNFTEVDVCBjLmlkLCBjLm5hbWUsIGMuY29kZSwgYy5pY29uLCBjLnBhcmVudF9pZCwgYy5kZXNjcmlwdGlvbiwgYy5wdWJsaXNoZWQgRlJPTSBjYXRlZ29yaWVzIGMgV0hFUkUgYy5wdWJsaXNoZWQgPSAxIEFORCBjLnBhcmVudF9pZCBJUyBOT1QgTlVMTCBPUkRFUiBCWSBjLm5hbWUgQVNDIExJTUlUIDEw"
        
    def get_input(self):
        """Lấy input từ user"""
        
        print("📝 INPUT:")
        print("-" * 30)
        
        # Get password
        password = input("Password (default: test): ").strip()
        if not password:
            password = "test"
            
        print()
        
        # Get encoded SQL
        print("Encoded SQL options:")
        print("1. Enter manually")
        print("2. Use sample encoded SQL")
        print("3. Read from file")
        
        choice = input("\nChoose option (1-3): ").strip()
        
        encoded_sql = ""
        
        if choice == "2":
            encoded_sql = self.get_sample_encoded_sql()
            print("✅ Using sample encoded SQL")
            
        elif choice == "3":
            filename = input("Enter filename: ").strip()
            try:
                with open(filename, 'r') as f:
                    encoded_sql = f.read().strip()
                print(f"✅ Read encoded SQL from {filename}")
            except Exception as e:
                print(f"❌ Error reading file: {e}")
                return None, None
                
        else:  # choice == "1" or default
            print("\nEnter encoded SQL (press Enter twice to finish):")
            lines = []
            while True:
                line = input()
                if line == "" and lines and lines[-1] == "":
                    break
                lines.append(line)
            encoded_sql = "".join(lines).strip()
            
        if not encoded_sql:
            print("❌ No encoded SQL provided!")
            return None, None
            
        return encoded_sql, password
        
    def call_api(self, encoded_sql, password):
        """Gọi API"""
        
        print("\n🔗 CALLING API:")
        print("-" * 30)
        print("⏳ Sending request...")
        
        try:
            # Prepare request data
            data = {
                "encodedSql": encoded_sql,
                "password": password
            }
            
            # Make API call
            response = requests.post(self.api_url, headers=self.headers, json=data, timeout=30)
            
            print(f"📡 Response status: {response.status_code}")
            
            # Process response
            if response.status_code == 200:
                result = response.json()
                return self.handle_success(result)
            else:
                print(f"❌ API Error: {response.status_code}")
                print(f"Response: {response.text}")
                return None
                
        except requests.exceptions.Timeout:
            print("❌ Request timeout (30s)")
            return None
        except requests.exceptions.ConnectionError:
            print("❌ Connection error - Check internet connection")
            return None
        except Exception as e:
            print(f"❌ Error: {str(e)}")
            return None
            
    def handle_success(self, result):
        """Xử lý khi API thành công"""
        
        if result.get('status') == '01' and result.get('message') == 'Successful!':
            data = result.get('data', [])
            
            if data:
                print(f"✅ Success! Retrieved {len(data)} records")
                return data
            else:
                print("✅ API call successful but no data returned")
                return []
        else:
            error_msg = result.get('message', 'Unknown error')
            print(f"❌ API returned error: {error_msg}")
            return None
            
    def display_data(self, data):
        """Hiển thị dữ liệu"""
        
        if not data:
            print("No data to display")
            return
            
        print(f"\n📊 RESULTS ({len(data)} records):")
        print("=" * 60)
        
        # Get all columns
        columns = set()
        for item in data:
            if isinstance(item, dict):
                columns.update(item.keys())
        columns = sorted(list(columns))
        
        # Display header
        header = " | ".join(f"{col[:15]:15}" for col in columns)
        print(header)
        print("-" * len(header))
        
        # Display data
        for i, item in enumerate(data):
            if isinstance(item, dict):
                row_values = []
                for col in columns:
                    value = item.get(col, '')
                    if value is None:
                        value = ''
                    value_str = str(value)
                    if len(value_str) > 15:
                        value_str = value_str[:12] + '...'
                    row_values.append(f"{value_str:15}")
                
                row = " | ".join(row_values)
                print(f"{i+1:2}. {row}")
                
        print("-" * len(header))
        print(f"Total: {len(data)} records")
        
    def export_options(self, data):
        """Tùy chọn export"""
        
        if not data:
            return
            
        print(f"\n📤 EXPORT OPTIONS:")
        print("-" * 30)
        print("1. Export to CSV")
        print("2. Export to JSON")
        print("3. View detailed record")
        print("4. Skip export")
        
        choice = input("\nChoose option (1-4): ").strip()
        
        if choice == "1":
            self.export_csv(data)
        elif choice == "2":
            self.export_json(data)
        elif choice == "3":
            self.view_detail(data)
        else:
            print("Skipping export")
            
    def export_csv(self, data):
        """Export ra CSV"""
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"vipomart_data_{timestamp}.csv"
        
        try:
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                if data:
                    fieldnames = data[0].keys()
                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                    writer.writeheader()
                    writer.writerows(data)
                    
            print(f"✅ Data exported to: {filename}")
            print(f"📁 Full path: {os.path.abspath(filename)}")
            
        except Exception as e:
            print(f"❌ Failed to export CSV: {str(e)}")
            
    def export_json(self, data):
        """Export ra JSON"""
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"vipomart_data_{timestamp}.json"
        
        try:
            with open(filename, 'w', encoding='utf-8') as jsonfile:
                json.dump(data, jsonfile, indent=2, ensure_ascii=False)
                
            print(f"✅ Data exported to: {filename}")
            print(f"📁 Full path: {os.path.abspath(filename)}")
            
        except Exception as e:
            print(f"❌ Failed to export JSON: {str(e)}")
            
    def view_detail(self, data):
        """Xem chi tiết record"""
        
        if not data:
            return
            
        print(f"\nEnter record number (1-{len(data)}): ", end="")
        try:
            record_num = int(input().strip())
            if 1 <= record_num <= len(data):
                record = data[record_num - 1]
                
                print(f"\n📋 RECORD #{record_num} DETAILS:")
                print("=" * 40)
                
                for key, value in record.items():
                    print(f"{key.replace('_', ' ').title():20}: {value}")
                    
                print("=" * 40)
            else:
                print("❌ Invalid record number")
                
        except ValueError:
            print("❌ Please enter a valid number")
            
    def run(self):
        """Chạy ứng dụng"""
        
        self.print_banner()
        
        # Get input
        encoded_sql, password = self.get_input()
        if not encoded_sql or not password:
            print("❌ Missing required input")
            return
            
        # Call API
        data = self.call_api(encoded_sql, password)
        if data is None:
            print("❌ API call failed")
            return
            
        # Display results
        self.display_data(data)
        
        # Export options
        if data:
            self.export_options(data)
            
        print(f"\n✅ Done! Time: {datetime.now().strftime('%H:%M:%S')}")


def main():
    """Main function"""
    
    try:
        caller = TerminalAPICaller()
        caller.run()
        
    except KeyboardInterrupt:
        print("\n\n👋 Goodbye!")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
