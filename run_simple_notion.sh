#!/bin/bash

echo "🚀 Starting Simple Notion Clone..."

# Kill any existing instances
pkill -f "python3.*notion" 2>/dev/null

# Check dependencies
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 not installed!"
    echo "Run: sudo apt install python3"
    exit 1
fi

if ! python3 -c "import tkinter" &> /dev/null; then
    echo "❌ tkinter not installed!"
    echo "Run: sudo apt install python3-tk"
    exit 1
fi

echo "✅ Dependencies OK"
echo "📝 Launching Simple Notion Clone..."

# Run the app
python3 simple_notion.py
