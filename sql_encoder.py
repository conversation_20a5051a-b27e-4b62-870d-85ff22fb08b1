#!/usr/bin/env python3
"""
SQL Encoder - <PERSON>ã hóa SQL thành encodedSql sử dụng AES/GCM
Tương thích với Java AESUtil
"""

import tkinter as tk
from tkinter import ttk, messagebox
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.backends import default_backend
import base64
import os
import secrets

class SQLEncoder:
    def __init__(self, root):
        self.root = root
        self.root.title("SQL Encoder - AES/GCM Encryption")
        self.root.geometry("800x600")
        self.root.minsize(600, 400)
        
        # Constants (same as Java)
        self.GCM_TAG_LENGTH = 16  # 128 bits = 16 bytes
        self.GCM_IV_LENGTH = 12   # 96 bits = 12 bytes
        self.ITERATION_COUNT = 65536
        self.KEY_LENGTH = 32      # 256 bits = 32 bytes
        self.SALT_LENGTH = 16     # 128 bits = 16 bytes
        
        self.create_widgets()
        
    def create_widgets(self):
        """Tạo giao diện"""
        
        # Main container
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        title_label = ttk.Label(main_frame, text="🔐 SQL Encoder - AES/GCM Encryption", 
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # Password section
        password_frame = ttk.LabelFrame(main_frame, text="Password", padding="10")
        password_frame.pack(fill=tk.X, pady=(0, 10))
        
        password_input_frame = ttk.Frame(password_frame)
        password_input_frame.pack(fill=tk.X)
        
        ttk.Label(password_input_frame, text="Password:").pack(side=tk.LEFT)
        self.password_var = tk.StringVar(value="test")
        self.password_entry = ttk.Entry(password_input_frame, textvariable=self.password_var, 
                                       show="*", width=20, font=("Arial", 12))
        self.password_entry.pack(side=tk.LEFT, padx=(10, 0))
        
        # Show/Hide password
        self.show_password_var = tk.BooleanVar()
        show_password_cb = ttk.Checkbutton(password_input_frame, text="Show", 
                                          variable=self.show_password_var,
                                          command=self.toggle_password)
        show_password_cb.pack(side=tk.LEFT, padx=(10, 0))
        
        # SQL Input section
        sql_frame = ttk.LabelFrame(main_frame, text="SQL Query Input", padding="10")
        sql_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # SQL text area
        sql_text_frame = ttk.Frame(sql_frame)
        sql_text_frame.pack(fill=tk.BOTH, expand=True)
        
        self.sql_text = tk.Text(sql_text_frame, wrap=tk.WORD, font=("Courier", 11),
                               height=8)
        sql_scrollbar = ttk.Scrollbar(sql_text_frame, orient=tk.VERTICAL, 
                                     command=self.sql_text.yview)
        self.sql_text.configure(yscrollcommand=sql_scrollbar.set)
        
        self.sql_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        sql_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Sample SQL button
        sample_btn = ttk.Button(sql_frame, text="Load Sample SQL", 
                               command=self.load_sample_sql)
        sample_btn.pack(pady=(10, 0))
        
        # Control buttons
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.encrypt_btn = ttk.Button(control_frame, text="🔐 Encrypt SQL", 
                                     command=self.encrypt_sql)
        self.encrypt_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.clear_btn = ttk.Button(control_frame, text="🗑️ Clear All", 
                                   command=self.clear_all)
        self.clear_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.copy_btn = ttk.Button(control_frame, text="📋 Copy Result", 
                                  command=self.copy_result, state=tk.DISABLED)
        self.copy_btn.pack(side=tk.RIGHT)
        
        # Result section
        result_frame = ttk.LabelFrame(main_frame, text="Encoded SQL Result", padding="10")
        result_frame.pack(fill=tk.BOTH, expand=True)
        
        # Result text area
        result_text_frame = ttk.Frame(result_frame)
        result_text_frame.pack(fill=tk.BOTH, expand=True)
        
        self.result_text = tk.Text(result_text_frame, wrap=tk.WORD, font=("Courier", 10),
                                  height=6, state=tk.DISABLED)
        result_scrollbar = ttk.Scrollbar(result_text_frame, orient=tk.VERTICAL, 
                                        command=self.result_text.yview)
        self.result_text.configure(yscrollcommand=result_scrollbar.set)
        
        self.result_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        result_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Status bar
        self.status_var = tk.StringVar(value="Ready")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.pack(fill=tk.X, pady=(10, 0))
        
    def toggle_password(self):
        """Toggle hiển thị password"""
        if self.show_password_var.get():
            self.password_entry.configure(show='')
        else:
            self.password_entry.configure(show='*')
            
    def load_sample_sql(self):
        """Load SQL mẫu"""
        sample_sql = """SELECT 
    c.id,
    c.name,
    c.code,
    c.icon,
    c.parent_id,
    c.description,
    c.published
FROM categories c 
WHERE c.published = 1 
    AND c.parent_id IS NOT NULL
ORDER BY c.name ASC
LIMIT 10"""
        
        self.sql_text.delete("1.0", tk.END)
        self.sql_text.insert("1.0", sample_sql)
        self.status_var.set("Sample SQL loaded")
        
    def generate_salt(self):
        """Tạo salt ngẫu nhiên"""
        return secrets.token_bytes(self.SALT_LENGTH)
        
    def generate_iv(self):
        """Tạo IV ngẫu nhiên"""
        return secrets.token_bytes(self.GCM_IV_LENGTH)
        
    def derive_key_from_password(self, password, salt):
        """Tạo key từ password và salt sử dụng PBKDF2"""
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=self.KEY_LENGTH,
            salt=salt,
            iterations=self.ITERATION_COUNT,
            backend=default_backend()
        )
        return kdf.derive(password.encode('utf-8'))
        
    def encrypt_aes_gcm(self, plaintext, password):
        """Mã hóa text sử dụng AES/GCM"""
        try:
            # Generate random salt and IV
            salt = self.generate_salt()
            iv = self.generate_iv()
            
            # Derive key from password
            key = self.derive_key_from_password(password, salt)
            
            # Create cipher
            cipher = Cipher(
                algorithms.AES(key),
                modes.GCM(iv),
                backend=default_backend()
            )
            encryptor = cipher.encryptor()
            
            # Encrypt
            ciphertext = encryptor.update(plaintext.encode('utf-8')) + encryptor.finalize()
            
            # Get authentication tag
            tag = encryptor.tag
            
            # Combine salt + iv + ciphertext + tag
            combined = salt + iv + ciphertext + tag
            
            # Encode to base64
            return base64.b64encode(combined).decode('utf-8')
            
        except Exception as e:
            raise Exception(f"Encryption failed: {str(e)}")
            
    def encrypt_sql(self):
        """Mã hóa SQL"""
        
        sql_query = self.sql_text.get("1.0", tk.END).strip()
        password = self.password_var.get().strip()
        
        if not sql_query:
            messagebox.showwarning("Warning", "Please enter SQL query!")
            return
            
        if not password:
            messagebox.showwarning("Warning", "Please enter password!")
            return
            
        try:
            # Encrypt SQL
            self.status_var.set("Encrypting...")
            self.root.update()
            
            encoded_sql = self.encrypt_aes_gcm(sql_query, password)
            
            # Display result
            self.result_text.configure(state=tk.NORMAL)
            self.result_text.delete("1.0", tk.END)
            self.result_text.insert("1.0", encoded_sql)
            self.result_text.configure(state=tk.DISABLED)
            
            # Enable copy button
            self.copy_btn.configure(state=tk.NORMAL)
            
            self.status_var.set(f"Encryption successful! Length: {len(encoded_sql)} characters")
            
        except Exception as e:
            messagebox.showerror("Error", f"Encryption failed: {str(e)}")
            self.status_var.set("Encryption failed")
            
    def copy_result(self):
        """Copy kết quả vào clipboard"""
        
        result = self.result_text.get("1.0", tk.END).strip()
        if result:
            self.root.clipboard_clear()
            self.root.clipboard_append(result)
            messagebox.showinfo("Success", "Encoded SQL copied to clipboard!")
            self.status_var.set("Copied to clipboard")
        else:
            messagebox.showwarning("Warning", "No result to copy!")
            
    def clear_all(self):
        """Xóa tất cả"""
        
        self.sql_text.delete("1.0", tk.END)
        self.result_text.configure(state=tk.NORMAL)
        self.result_text.delete("1.0", tk.END)
        self.result_text.configure(state=tk.DISABLED)
        self.copy_btn.configure(state=tk.DISABLED)
        self.status_var.set("Cleared")


def main():
    """Main function"""
    root = tk.Tk()
    app = SQLEncoder(root)
    
    def on_closing():
        if messagebox.askokcancel("Exit", "Exit SQL Encoder?"):
            root.destroy()
    
    root.protocol("WM_DELETE_WINDOW", on_closing)
    root.mainloop()


if __name__ == "__main__":
    main()
