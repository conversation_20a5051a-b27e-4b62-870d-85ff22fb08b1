#!/usr/bin/env python3
"""
API Call App - Gọi API Vipomart và hiển thị dữ liệu dạng bảng
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import requests
import json
import csv
from datetime import datetime
import threading

class APICallApp:
    def __init__(self, root):
        self.root = root
        self.root.title("API Call App - Vipomart Query")
        self.root.geometry("1200x800")
        self.root.minsize(800, 600)
        
        # API Configuration
        self.api_url = "https://api.vipomart.vn/authen/query"
        self.headers = {
            'X-Internal-Secret': 'Nv82Lx7Kq!Dfj29slPq2R5mxVfLzXp9A',
            'User-Agent': 'Apidog/1.0.0 (https://apidog.com)',
            'Content-Type': 'application/json',
            'Cookie': 'JSESSIONID=F17B9DB321617A561FEC4966FDEFDBD3; SERVERID=A; JSESSIONID=D61C911FBB86F7CCF49CEF37540D0877; JSESSIONID=AA2727DFD8B4C62D662DA0CEEE4513D6; JSESSIONID=E99659309A054438C6D8C3D0DFC5848B'
        }
        
        # Data storage
        self.current_data = []
        self.filtered_data = []
        
        # Create UI
        self.create_widgets()
        
    def create_widgets(self):
        """Tạo giao diện"""
        
        # Main container
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        title_label = ttk.Label(main_frame, text="API Call App - Vipomart Query", 
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # Input section
        self.create_input_section(main_frame)
        
        # Control buttons
        self.create_control_section(main_frame)
        
        # Results section
        self.create_results_section(main_frame)
        
        # Status bar
        self.create_status_bar(main_frame)
        
    def create_input_section(self, parent):
        """Tạo phần input"""
        
        input_frame = ttk.LabelFrame(parent, text="Query Input", padding="10")
        input_frame.pack(fill=tk.X, pady=(0, 10))
        
        # EncodedSql input
        ttk.Label(input_frame, text="Encoded SQL:").pack(anchor=tk.W)
        
        sql_frame = ttk.Frame(input_frame)
        sql_frame.pack(fill=tk.X, pady=(5, 10))
        
        self.sql_text = tk.Text(sql_frame, height=4, wrap=tk.WORD, font=("Courier", 10))
        sql_scrollbar = ttk.Scrollbar(sql_frame, orient=tk.VERTICAL, command=self.sql_text.yview)
        self.sql_text.configure(yscrollcommand=sql_scrollbar.set)
        
        self.sql_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        sql_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Password input
        password_frame = ttk.Frame(input_frame)
        password_frame.pack(fill=tk.X)
        
        ttk.Label(password_frame, text="Password:").pack(side=tk.LEFT)
        self.password_var = tk.StringVar(value="test")
        self.password_entry = ttk.Entry(password_frame, textvariable=self.password_var, show="*", width=20)
        self.password_entry.pack(side=tk.LEFT, padx=(10, 0))
        
        # Show/Hide password
        self.show_password_var = tk.BooleanVar()
        show_password_cb = ttk.Checkbutton(password_frame, text="Show", 
                                          variable=self.show_password_var,
                                          command=self.toggle_password)
        show_password_cb.pack(side=tk.LEFT, padx=(10, 0))
        
    def create_control_section(self, parent):
        """Tạo phần control buttons"""
        
        control_frame = ttk.Frame(parent)
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Left side buttons
        left_frame = ttk.Frame(control_frame)
        left_frame.pack(side=tk.LEFT)
        
        self.call_button = ttk.Button(left_frame, text="Call API", 
                                     command=self.call_api)
        self.call_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.clear_button = ttk.Button(left_frame, text="Clear", 
                                      command=self.clear_data)
        self.clear_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # Right side buttons
        right_frame = ttk.Frame(control_frame)
        right_frame.pack(side=tk.RIGHT)
        
        self.export_button = ttk.Button(right_frame, text="Export CSV", 
                                       command=self.export_csv, state=tk.DISABLED)
        self.export_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.copy_button = ttk.Button(right_frame, text="Copy Data", 
                                     command=self.copy_data, state=tk.DISABLED)
        self.copy_button.pack(side=tk.LEFT)
        
    def create_results_section(self, parent):
        """Tạo phần hiển thị kết quả"""
        
        results_frame = ttk.LabelFrame(parent, text="Results", padding="10")
        results_frame.pack(fill=tk.BOTH, expand=True)
        
        # Search bar
        search_frame = ttk.Frame(results_frame)
        search_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(search_frame, text="Search:").pack(side=tk.LEFT)
        self.search_var = tk.StringVar()
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var, width=30)
        search_entry.pack(side=tk.LEFT, padx=(10, 0))
        search_entry.bind('<KeyRelease>', self.on_search)
        
        # Results info
        self.info_var = tk.StringVar(value="No data")
        info_label = ttk.Label(search_frame, textvariable=self.info_var)
        info_label.pack(side=tk.RIGHT)
        
        # Treeview for results
        tree_frame = ttk.Frame(results_frame)
        tree_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create treeview with scrollbars
        self.tree = ttk.Treeview(tree_frame, show="headings")
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.tree.yview)
        h_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        self.tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Pack treeview and scrollbars
        self.tree.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")
        
        # Configure grid weights
        tree_frame.grid_rowconfigure(0, weight=1)
        tree_frame.grid_columnconfigure(0, weight=1)
        
        # Bind double click
        self.tree.bind("<Double-1>", self.on_item_double_click)
        
    def create_status_bar(self, parent):
        """Tạo status bar"""
        
        status_frame = ttk.Frame(parent)
        status_frame.pack(fill=tk.X, pady=(10, 0))
        
        self.status_var = tk.StringVar(value="Ready")
        status_bar = ttk.Label(status_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.pack(fill=tk.X)
        
        # Progress bar
        self.progress = ttk.Progressbar(status_frame, mode='indeterminate')
        self.progress.pack(fill=tk.X, pady=(5, 0))
        
    def toggle_password(self):
        """Toggle hiển thị password"""
        if self.show_password_var.get():
            self.password_entry.configure(show='')
        else:
            self.password_entry.configure(show='*')
            
    def call_api(self):
        """Gọi API trong thread riêng"""
        
        encoded_sql = self.sql_text.get("1.0", tk.END).strip()
        password = self.password_var.get().strip()
        
        if not encoded_sql:
            messagebox.showwarning("Warning", "Please enter Encoded SQL!")
            return
            
        if not password:
            messagebox.showwarning("Warning", "Please enter Password!")
            return
            
        # Disable button and start progress
        self.call_button.configure(state=tk.DISABLED)
        self.progress.start()
        self.status_var.set("Calling API...")
        
        # Call API in separate thread
        thread = threading.Thread(target=self._call_api_thread, args=(encoded_sql, password))
        thread.daemon = True
        thread.start()
        
    def _call_api_thread(self, encoded_sql, password):
        """Gọi API trong thread"""
        
        try:
            # Prepare request data
            data = {
                "encodedSql": encoded_sql,
                "password": password
            }
            
            # Make API call
            response = requests.post(self.api_url, headers=self.headers, json=data, timeout=30)
            
            # Process response
            if response.status_code == 200:
                result = response.json()
                
                # Update UI in main thread
                self.root.after(0, self._handle_api_success, result)
            else:
                error_msg = f"API Error: {response.status_code} - {response.text}"
                self.root.after(0, self._handle_api_error, error_msg)
                
        except requests.exceptions.Timeout:
            self.root.after(0, self._handle_api_error, "Request timeout (30s)")
        except requests.exceptions.ConnectionError:
            self.root.after(0, self._handle_api_error, "Connection error")
        except Exception as e:
            self.root.after(0, self._handle_api_error, f"Error: {str(e)}")
            
    def _handle_api_success(self, result):
        """Xử lý khi API thành công"""
        
        # Stop progress and enable button
        self.progress.stop()
        self.call_button.configure(state=tk.NORMAL)
        
        # Check API response status
        if result.get('status') == '01' and result.get('message') == 'Successful!':
            data = result.get('data', [])
            
            if data:
                self.current_data = data
                self.filtered_data = data.copy()
                self.display_data(data)
                self.status_var.set(f"Success! Retrieved {len(data)} records at {datetime.now().strftime('%H:%M:%S')}")
                
                # Enable export buttons
                self.export_button.configure(state=tk.NORMAL)
                self.copy_button.configure(state=tk.NORMAL)
            else:
                self.status_var.set("Success but no data returned")
                messagebox.showinfo("Info", "API call successful but no data returned")
        else:
            error_msg = f"API returned error: {result.get('message', 'Unknown error')}"
            self.status_var.set("API Error")
            messagebox.showerror("API Error", error_msg)
            
    def _handle_api_error(self, error_msg):
        """Xử lý khi API lỗi"""
        
        # Stop progress and enable button
        self.progress.stop()
        self.call_button.configure(state=tk.NORMAL)
        
        self.status_var.set("API call failed")
        messagebox.showerror("Error", error_msg)

    def display_data(self, data):
        """Hiển thị dữ liệu trong bảng"""

        if not data:
            return

        # Clear existing data
        for item in self.tree.get_children():
            self.tree.delete(item)

        # Get all unique columns from data
        columns = set()
        for item in data:
            if isinstance(item, dict):
                columns.update(item.keys())
        columns = sorted(list(columns))

        # Configure treeview columns
        self.tree["columns"] = columns

        # Configure column headings and widths
        for col in columns:
            self.tree.heading(col, text=col.replace('_', ' ').title())
            self.tree.column(col, width=120, minwidth=80)

        # Insert data
        for i, item in enumerate(data):
            if isinstance(item, dict):
                values = []
                for col in columns:
                    value = item.get(col, '')
                    # Handle None values and long text
                    if value is None:
                        value = ''
                    elif isinstance(value, str) and len(value) > 50:
                        value = value[:47] + '...'
                    values.append(str(value))

                self.tree.insert("", "end", values=values, tags=(f"row{i % 2}",))

        # Configure row colors
        self.tree.tag_configure("row0", background="#ffffff")
        self.tree.tag_configure("row1", background="#f0f0f0")

        # Update info
        self.update_info()

    def update_info(self):
        """Cập nhật thông tin hiển thị"""
        total = len(self.current_data)
        filtered = len(self.filtered_data)

        if total == filtered:
            self.info_var.set(f"Total: {total} records")
        else:
            self.info_var.set(f"Showing: {filtered} / {total} records")

    def on_search(self, event=None):
        """Xử lý tìm kiếm"""

        search_term = self.search_var.get().lower().strip()

        if not search_term:
            self.filtered_data = self.current_data.copy()
        else:
            self.filtered_data = []
            for item in self.current_data:
                # Search in all values of the item
                item_text = ' '.join(str(v).lower() for v in item.values() if v is not None)
                if search_term in item_text:
                    self.filtered_data.append(item)

        self.display_data(self.filtered_data)

    def on_item_double_click(self, event):
        """Xử lý double click vào item"""

        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            values = item['values']
            columns = self.tree["columns"]

            # Create detail window
            self.show_item_detail(dict(zip(columns, values)))

    def show_item_detail(self, item_data):
        """Hiển thị chi tiết item"""

        detail_window = tk.Toplevel(self.root)
        detail_window.title("Item Detail")
        detail_window.geometry("600x400")
        detail_window.transient(self.root)

        # Create text widget with scrollbar
        text_frame = ttk.Frame(detail_window, padding="10")
        text_frame.pack(fill=tk.BOTH, expand=True)

        text_widget = tk.Text(text_frame, wrap=tk.WORD, font=("Courier", 10))
        scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)

        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Insert formatted data
        for key, value in item_data.items():
            text_widget.insert(tk.END, f"{key}:\n{value}\n\n")

        text_widget.configure(state=tk.DISABLED)

    def clear_data(self):
        """Xóa dữ liệu"""

        # Clear input
        self.sql_text.delete("1.0", tk.END)
        self.search_var.set("")

        # Clear data
        self.current_data = []
        self.filtered_data = []

        # Clear treeview
        for item in self.tree.get_children():
            self.tree.delete(item)

        # Reset UI
        self.export_button.configure(state=tk.DISABLED)
        self.copy_button.configure(state=tk.DISABLED)
        self.info_var.set("No data")
        self.status_var.set("Ready")

    def export_csv(self):
        """Export dữ liệu ra CSV"""

        if not self.filtered_data:
            messagebox.showwarning("Warning", "No data to export!")
            return

        # Ask for file location
        filename = filedialog.asksaveasfilename(
            defaultextension=".csv",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
            title="Save CSV file"
        )

        if filename:
            try:
                with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                    if self.filtered_data:
                        fieldnames = self.filtered_data[0].keys()
                        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                        writer.writeheader()
                        writer.writerows(self.filtered_data)

                messagebox.showinfo("Success", f"Data exported to {filename}")
                self.status_var.set(f"Exported {len(self.filtered_data)} records to CSV")

            except Exception as e:
                messagebox.showerror("Error", f"Failed to export: {str(e)}")

    def copy_data(self):
        """Copy dữ liệu vào clipboard"""

        if not self.filtered_data:
            messagebox.showwarning("Warning", "No data to copy!")
            return

        try:
            # Convert to tab-separated format
            if self.filtered_data:
                # Headers
                headers = list(self.filtered_data[0].keys())
                clipboard_text = '\t'.join(headers) + '\n'

                # Data rows
                for item in self.filtered_data:
                    row = []
                    for header in headers:
                        value = item.get(header, '')
                        row.append(str(value) if value is not None else '')
                    clipboard_text += '\t'.join(row) + '\n'

                # Copy to clipboard
                self.root.clipboard_clear()
                self.root.clipboard_append(clipboard_text)

                messagebox.showinfo("Success", f"Copied {len(self.filtered_data)} records to clipboard")
                self.status_var.set("Data copied to clipboard")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to copy: {str(e)}")


def main():
    """Main function"""
    root = tk.Tk()
    app = APICallApp(root)

    def on_closing():
        if messagebox.askokcancel("Exit", "Exit application?"):
            root.destroy()

    root.protocol("WM_DELETE_WINDOW", on_closing)
    root.mainloop()


if __name__ == "__main__":
    main()
