#!/usr/bin/env python3
"""
Simple Notion Clone - Tránh lỗi X11
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import json
import os
from datetime import datetime
import uuid

class SimpleNotionApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Simple Notion Clone")
        self.root.geometry("1000x700")
        
        # Data
        self.data_file = os.path.expanduser("~/.simple_notion_data.json")
        self.pages = []
        self.current_page = None
        
        # Create UI
        self.create_widgets()
        self.load_data()
        self.refresh_sidebar()
        
    def create_widgets(self):
        """Tạo giao diện đơn giản"""
        
        # Main container
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Configure grid
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(0, weight=1)
        
        # Sidebar
        sidebar_frame = ttk.LabelFrame(main_frame, text="Pages", width=200)
        sidebar_frame.grid(row=0, column=0, sticky="nsew", padx=(0, 10))
        sidebar_frame.grid_propagate(False)
        
        # New page button
        ttk.Button(sidebar_frame, text="+ New Page", 
                  command=self.create_new_page).pack(fill=tk.X, padx=5, pady=5)
        
        # Pages listbox
        self.pages_listbox = tk.Listbox(sidebar_frame)
        self.pages_listbox.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        self.pages_listbox.bind('<<ListboxSelect>>', self.on_page_select)
        
        # Main content area
        content_frame = ttk.LabelFrame(main_frame, text="Content")
        content_frame.grid(row=0, column=1, sticky="nsew")
        content_frame.columnconfigure(0, weight=1)
        content_frame.rowconfigure(1, weight=1)
        
        # Title entry
        title_frame = ttk.Frame(content_frame)
        title_frame.grid(row=0, column=0, sticky="ew", padx=10, pady=5)
        title_frame.columnconfigure(1, weight=1)
        
        ttk.Label(title_frame, text="Title:").grid(row=0, column=0, sticky="w")
        self.title_var = tk.StringVar()
        self.title_entry = ttk.Entry(title_frame, textvariable=self.title_var, font=("Arial", 14))
        self.title_entry.grid(row=0, column=1, sticky="ew", padx=(10, 0))
        self.title_var.trace('w', self.on_title_change)
        
        # Content text area
        text_frame = ttk.Frame(content_frame)
        text_frame.grid(row=1, column=0, sticky="nsew", padx=10, pady=5)
        text_frame.columnconfigure(0, weight=1)
        text_frame.rowconfigure(0, weight=1)
        
        self.content_text = tk.Text(text_frame, wrap=tk.WORD, font=("Arial", 11))
        scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.content_text.yview)
        self.content_text.configure(yscrollcommand=scrollbar.set)
        
        self.content_text.grid(row=0, column=0, sticky="nsew")
        scrollbar.grid(row=0, column=1, sticky="ns")
        
        # Bind content change
        self.content_text.bind('<KeyRelease>', self.on_content_change)
        
        # Toolbar
        toolbar_frame = ttk.Frame(content_frame)
        toolbar_frame.grid(row=2, column=0, sticky="ew", padx=10, pady=5)
        
        ttk.Button(toolbar_frame, text="H1", command=lambda: self.insert_text("# ")).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar_frame, text="H2", command=lambda: self.insert_text("## ")).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar_frame, text="List", command=lambda: self.insert_text("- ")).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar_frame, text="Quote", command=lambda: self.insert_text("> ")).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar_frame, text="Code", command=lambda: self.insert_text("```\n\n```")).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar_frame, text="Todo", command=lambda: self.insert_text("- [ ] ")).pack(side=tk.LEFT, padx=2)
        
        # Delete button
        ttk.Button(toolbar_frame, text="Delete Page", command=self.delete_page).pack(side=tk.RIGHT)
        
        # Status bar
        self.status_var = tk.StringVar()
        self.status_var.set("Ready")
        status_bar = ttk.Label(content_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.grid(row=3, column=0, sticky="ew", padx=10, pady=5)
        
        # Initially disable content area
        self.set_content_state(False)
        
    def set_content_state(self, enabled):
        """Enable/disable content area"""
        state = tk.NORMAL if enabled else tk.DISABLED
        self.title_entry.configure(state=state)
        self.content_text.configure(state=state)
        
    def insert_text(self, text):
        """Insert text at cursor position"""
        if self.current_page:
            self.content_text.insert(tk.INSERT, text)
            self.content_text.focus()
            
    def create_new_page(self):
        """Create new page"""
        title = simpledialog.askstring("New Page", "Enter page title:", initialvalue="Untitled")
        if title:
            new_page = {
                'id': str(uuid.uuid4()),
                'title': title,
                'content': '',
                'created_date': self.get_current_time(),
                'modified_date': self.get_current_time()
            }
            self.pages.append(new_page)
            self.save_data()
            self.refresh_sidebar()
            self.select_page(new_page)
            
    def on_page_select(self, event):
        """Handle page selection"""
        selection = self.pages_listbox.curselection()
        if selection:
            index = selection[0]
            if 0 <= index < len(self.pages):
                self.select_page(self.pages[index])
                
    def select_page(self, page):
        """Select and display page"""
        self.current_page = page
        self.title_var.set(page['title'])
        
        self.content_text.configure(state=tk.NORMAL)
        self.content_text.delete("1.0", tk.END)
        self.content_text.insert("1.0", page['content'])
        
        self.set_content_state(True)
        self.update_status(f"Editing: {page['title']}")
        
    def on_title_change(self, *args):
        """Handle title change"""
        if self.current_page:
            new_title = self.title_var.get()
            if new_title != self.current_page['title']:
                self.current_page['title'] = new_title or "Untitled"
                self.current_page['modified_date'] = self.get_current_time()
                self.save_data()
                self.refresh_sidebar()
                
    def on_content_change(self, event=None):
        """Handle content change"""
        if self.current_page:
            content = self.content_text.get("1.0", tk.END).rstrip()
            if content != self.current_page['content']:
                self.current_page['content'] = content
                self.current_page['modified_date'] = self.get_current_time()
                self.save_data()
                self.update_status("Auto-saved")
                
    def delete_page(self):
        """Delete current page"""
        if self.current_page:
            if messagebox.askyesno("Delete Page", f"Delete '{self.current_page['title']}'?"):
                self.pages = [p for p in self.pages if p['id'] != self.current_page['id']]
                self.current_page = None
                self.save_data()
                self.refresh_sidebar()
                self.clear_content()
                
    def clear_content(self):
        """Clear content area"""
        self.title_var.set("")
        self.content_text.configure(state=tk.NORMAL)
        self.content_text.delete("1.0", tk.END)
        self.set_content_state(False)
        self.update_status("No page selected")
        
    def refresh_sidebar(self):
        """Refresh pages list"""
        self.pages_listbox.delete(0, tk.END)
        for page in self.pages:
            self.pages_listbox.insert(tk.END, page['title'])
            
        # Select current page
        if self.current_page:
            for i, page in enumerate(self.pages):
                if page['id'] == self.current_page['id']:
                    self.pages_listbox.selection_set(i)
                    break
                    
    def update_status(self, message):
        """Update status bar"""
        self.status_var.set(f"{message} - {self.get_current_time()}")
        
    def load_data(self):
        """Load data from file"""
        try:
            if os.path.exists(self.data_file):
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    self.pages = json.load(f)
        except Exception as e:
            messagebox.showerror("Error", f"Cannot load data: {str(e)}")
            self.pages = []
            
    def save_data(self):
        """Save data to file"""
        try:
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(self.pages, f, ensure_ascii=False, indent=2)
        except Exception as e:
            messagebox.showerror("Error", f"Cannot save data: {str(e)}")
            
    def get_current_time(self):
        """Get current time"""
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")


def main():
    root = tk.Tk()
    app = SimpleNotionApp(root)
    
    def on_closing():
        if messagebox.askokcancel("Exit", "Exit application?"):
            root.destroy()
    
    root.protocol("WM_DELETE_WINDOW", on_closing)
    root.mainloop()


if __name__ == "__main__":
    main()
