# 🔐 Integrated SQL Encoder + API Caller

Ứng dụng tích hợp đầy đủ để mã hóa SQL và gọi API Vipomart trong một giao diện duy nhất.

## ✨ Tính năng chính

### 🔐 SQL Encoder Tab
- **AES/GCM Encryption** tương thích với Java AESUtil
- **Password-based encryption** với PBKDF2
- **Sample SQL** để test nhanh
- **Copy encoded result** vào clipboard
- **One-click encrypt & call API**

### 🔗 API Caller Tab  
- **Vipomart API integration** với đúng headers
- **Dynamic table** hiển thị kết quả
- **Real-time search** và filtering
- **Export CSV** và copy to clipboard
- **Double-click** để xem chi tiết
- **Threading** để tránh đơ UI

### 🎯 Workflow tích hợp
- **Tab switching** liền mạch
- **Auto-paste** encoded SQL giữa tabs
- **Shared password** cho cả encoder và API
- **Status bar** thống nhất

## 🚀 Cách sử dụng

### Khởi động
```bash
# Script tự động cài dependencies
./run_integrated_app.sh

# Hoặc chạy trực tiếp
python3 integrated_app.py
```

### Workflow hoàn chỉnh

#### **Cách 1: Từng bước**
1. **Tab "SQL Encoder"**:
   - Nhập password (mặc định: "test")
   - Gõ SQL query hoặc click "Load Sample SQL"
   - Click "🔐 Encrypt SQL"
   - Copy kết quả nếu cần

2. **Tab "API Caller"**:
   - Click "📋 Paste from Encoder" 
   - Click "🔗 Call API"
   - Xem kết quả trong bảng
   - Search, export, copy data

#### **Cách 2: One-click**
1. **Tab "SQL Encoder"**:
   - Nhập SQL query
   - Click "🚀 Encrypt & Call API"
   - Tự động chuyển tab và gọi API
   - Xem kết quả ngay lập tức

## 🔧 Encryption Details

### Algorithm: AES/GCM
- **Key derivation**: PBKDF2 với SHA256
- **Iterations**: 65,536
- **Key length**: 256 bits
- **IV length**: 96 bits (12 bytes)
- **Salt length**: 128 bits (16 bytes)
- **Tag length**: 128 bits (16 bytes)

### Format
```
Base64(salt + iv + ciphertext + tag)
```

### Tương thích Java
```java
// Java code tương đương
AESUtil.encrypt(sqlQuery, password, salt)
```

## 📊 API Integration

### Endpoint
```
POST https://api.vipomart.vn/authen/query
```

### Request
```json
{
    "encodedSql": "base64_encrypted_sql",
    "password": "test"
}
```

### Response
```json
{
    "status": "01",
    "message": "Successful!",
    "data": [
        {
            "id": 1625441,
            "name": "Cháo gói, cháo tươi",
            "code": "MMCP_chaog",
            // ... other fields
        }
    ]
}
```

## 🎨 Giao diện

```
┌─────────────────────────────────────────────────────────────┐
│        🔐 SQL Encoder + API Caller - Vipomart              │
├─────────────────────────────────────────────────────────────┤
│ [🔐 SQL Encoder] [🔗 API Caller]                           │
├─────────────────────────────────────────────────────────────┤
│ SQL Encoder Tab:                                            │
│ Password: [test****] ☐ Show                                 │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ SELECT * FROM categories WHERE published = 1           │ │
│ │ ORDER BY name ASC LIMIT 10                             │ │
│ └─────────────────────────────────────────────────────────┘ │
│ [Sample SQL] [🔐 Encrypt] [🚀 Encrypt & Call API] [📋 Copy] │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ +wPz+LN4ehaLkjVbd7HxwjZ+NwLFsN3cgfLKEhzuuVd10QAkA... │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ API Caller Tab:                                             │
│ Encoded SQL: [paste_here]                                  │
│ [🔗 Call API] [📋 Paste] [🗑️ Clear] [📊 Export] [📋 Copy]   │
│ Search: [_______]                          Total: 5 records │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ ID    │ Name              │ Code        │ Icon          │ │
│ │ 1625441│ Cháo gói, cháo... │ MMCP_chaog  │ https://...   │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ Status: Success! Retrieved 5 records at 10:30:45           │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 Dependencies

### System Requirements
- **OS**: Ubuntu 18.04+
- **Python**: 3.6+
- **Internet**: Để gọi API

### Python Packages
```bash
pip3 install requests cryptography
```

### Auto-install
Script `run_integrated_app.sh` sẽ tự động cài đặt dependencies.

## 💡 Tips sử dụng

### SQL Examples
```sql
-- Categories
SELECT id, name, code FROM categories WHERE published = 1;

-- Products  
SELECT p.id, p.name, c.name as category 
FROM products p 
JOIN categories c ON p.category_id = c.id;

-- Count records
SELECT COUNT(*) as total FROM categories;
```

### Keyboard Shortcuts
- **Tab**: Chuyển giữa các fields
- **Ctrl+A**: Select all text
- **Ctrl+C/V**: Copy/Paste
- **Enter**: Submit trong password field

### Best Practices
1. **Test với sample SQL** trước khi dùng query phức tạp
2. **Copy encoded SQL** để backup
3. **Export CSV** để lưu kết quả
4. **Use search** để filter large datasets

## 🐛 Troubleshooting

### Lỗi "cryptography not found"
```bash
pip3 install cryptography
```

### Lỗi "Encryption failed"
- Kiểm tra password không rỗng
- Kiểm tra SQL query hợp lệ
- Restart app nếu cần

### Lỗi "API call failed"
- Kiểm tra internet connection
- Kiểm tra encoded SQL đúng format
- Thử với sample SQL

### Lỗi "No data returned"
- SQL query có thể không trả về kết quả
- Kiểm tra WHERE conditions
- Thử query đơn giản hơn

## 🚧 Tính năng mở rộng

- [ ] **SQL Syntax Highlighting**
- [ ] **Query History** 
- [ ] **Multiple API Endpoints**
- [ ] **Custom Headers Editor**
- [ ] **Data Visualization Charts**
- [ ] **Batch Query Processing**
- [ ] **Query Performance Metrics**

## 📄 License

MIT License - Sử dụng tự do.

---

**Happy SQL encoding and API calling! 🚀**
