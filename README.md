# 📝 Ứng dụng Ghi chú - Notes App

Ứng dụng ghi chú đơn giản với giao diện dạng bảng, đ<PERSON><PERSON><PERSON> phát triển bằng Python và tkinter.

## ✨ Tính năng

- 📋 Giao diện dạng bảng hiển thị danh sách ghi chú
- ➕ Thêm ghi chú mới
- ✏️ Sửa ghi chú đã có
- 🗑️ Xóa ghi chú
- 💾 Tự động lưu dữ liệu vào file JSON
- 🔄 Làm mới danh sách
- 📅 Hiển thị ngày tạo và ngày sửa
- 🖱️ Double-click để sửa nhanh

## 🚀 Cài đặt

### Cách 1: Chạy trực tiếp từ Python

```bash
# Cài đặt Python 3 (nếu chưa có)
sudo apt update
sudo apt install python3 python3-pip python3-tk

# Chạy ứng dụng
python3 notes_app.py
```

### Cách 2: Cài đặt như một package Python

```bash
# Cài đặt từ source
pip3 install .

# Chạy ứng dụng
notes-app
```

### Cách 3: Tạo file .deb để cài đặt

```bash
# Cài đặt các công cụ cần thiết
sudo apt install python3-stdeb dh-python

# Tạo file .deb
python3 setup.py --command-packages=stdeb.command bdist_deb

# Cài đặt file .deb
sudo dpkg -i deb_dist/notes-app_1.0.0-1_all.deb

# Nếu có lỗi dependency, chạy:
sudo apt-get install -f
```

## 📖 Hướng dẫn sử dụng

1. **Khởi động ứng dụng**: Chạy `python3 notes_app.py` hoặc `notes-app` (nếu đã cài đặt)

2. **Thêm ghi chú mới**: 
   - Click nút "➕ Thêm ghi chú"
   - Nhập tiêu đề và nội dung
   - Click "Lưu"

3. **Sửa ghi chú**:
   - Chọn ghi chú trong bảng
   - Click nút "✏️ Sửa ghi chú" hoặc double-click vào ghi chú
   - Chỉnh sửa và click "Lưu"

4. **Xóa ghi chú**:
   - Chọn ghi chú trong bảng
   - Click nút "🗑️ Xóa ghi chú"
   - Xác nhận xóa

5. **Làm mới**: Click nút "🔄 Làm mới" để cập nhật bảng

## 💾 Lưu trữ dữ liệu

Dữ liệu được lưu tự động vào file `~/.notes_app_data.json` trong thư mục home của bạn.

## 🔧 Yêu cầu hệ thống

- **Hệ điều hành**: Ubuntu 18.04+ (hoặc các distro Linux khác)
- **Python**: 3.6+
- **Thư viện**: tkinter (thường có sẵn với Python)

## 🐛 Báo lỗi

Nếu gặp lỗi, vui lòng tạo issue trên GitHub hoặc liên hệ với tác giả.

## 📄 Giấy phép

MIT License - Xem file LICENSE để biết thêm chi tiết.

## 🤝 Đóng góp

Mọi đóng góp đều được chào đón! Vui lòng tạo pull request hoặc issue.

## 📞 Liên hệ

- Tác giả: Assistant
- Email: <EMAIL>

---

**Lưu ý**: Ứng dụng này được phát triển cho mục đích học tập và sử dụng cá nhân.
