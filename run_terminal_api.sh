#!/bin/bash

echo "🔗 Starting Terminal API Caller..."

# Check Python3
if ! which python3 > /dev/null 2>&1; then
    echo "❌ Python3 not installed!"
    echo "Run: sudo apt install python3"
    exit 1
fi

# Check and install dependencies
echo "📦 Checking dependencies..."

if ! python3 -c "import requests" > /dev/null 2>&1; then
    echo "📦 Installing requests..."
    pip3 install requests
fi

echo "✅ Dependencies OK"
echo ""

# Run the app
python3 terminal_api_caller.py
