# 🔗 Direct API Caller - Vipomart

Ứng dụng đơn giản để gọi API Vipomart trực tiếp với encoded SQL - bỏ qua bước encoding.

## ✨ Tính năng chính

### 🎯 **Workflow đơn giản**
1. **<PERSON><PERSON><PERSON><PERSON> encoded SQL** → 2. **Nhập password** → 3. **Click Call API** → 4. **Xem kết quả**

### 🔧 **Tính năng chính**
- **Direct input**: Nhập encoded SQL trực tiếp
- **One-click API call**: Gọi API với một nút bấm
- **Real-time results**: Hiển thị kết quả ngay lập tức
- **Dynamic table**: Bảng tự động tạo columns theo data
- **Search & filter**: Tìm kiếm real-time trong kết quả
- **Export options**: CSV export và clipboard copy
- **Detail view**: Double-click để xem chi tiết item
- **Keyboard shortcuts**: <PERSON><PERSON><PERSON>+Enter, Ctrl+V, Ctrl+L

## 🚀 Cách sử dụng

### Khởi động nhanh
```bash
# Chạy ứng dụng
./run_direct_api.sh
```

### Workflow cơ bản

#### **Bước 1: Nhập dữ liệu**
- **Password**: Mặc định "test" (có thể thay đổi)
- **Encoded SQL**: Paste encoded SQL vào text area

#### **Bước 2: Gọi API**
- Click **"🚀 Call API"** hoặc nhấn **Ctrl+Enter**
- Chờ progress bar (tối đa 30 giây)

#### **Bước 3: Xem kết quả**
- Data hiển thị trong bảng động
- Sử dụng search để filter
- Double-click row để xem chi tiết
- Export CSV hoặc copy data

### Keyboard Shortcuts
- **Ctrl+Enter**: Call API
- **Ctrl+V**: Paste from clipboard
- **Ctrl+L**: Clear input
- **Double-click**: View item detail

## 🎨 Giao diện

```
┌─────────────────────────────────────────────────────────────┐
│              🔗 Direct API Caller - Vipomart               │
├─────────────────────────────────────────────────────────────┤
│ API Input                                                   │
│ Password: [test****] ☐ Show                                 │
│                                                             │
│ Encoded SQL:                                                │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ dGVzdF9lbmNyeXB0ZWRfc3FsX2RhdGFfaGVyZS4uLg==          │ │
│ │                                                         │ │
│ └─────────────────────────────────────────────────────────┘ │
│ [🚀 Call API] [📋 Paste] [🗑️ Clear] [📝 Sample] [📊 Export] │
├─────────────────────────────────────────────────────────────┤
│ API Results                                                 │
│ 🔍 Search: [_______]                    📊 Total: 5 records │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ ID      │ Name              │ Code        │ Icon        │ │
│ │ 1625441 │ Cháo gói, cháo... │ MMCP_chaog  │ https://... │ │
│ │ 1625442 │ Bánh mì kẹp...    │ MMCP_banh   │ https://... │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ ✅ Success! Retrieved 5 records at 10:30:45                │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 API Configuration

### Endpoint
```
POST https://api.vipomart.vn/authen/query
```

### Headers
```json
{
    "X-Internal-Secret": "Nv82Lx7Kq!Dfj29slPq2R5mxVfLzXp9A",
    "User-Agent": "Apidog/1.0.0 (https://apidog.com)",
    "Content-Type": "application/json",
    "Cookie": "JSESSIONID=F17B9DB321617A561FEC4966FDEFDBD3; ..."
}
```

### Request Body
```json
{
    "encodedSql": "your_base64_encoded_sql_here",
    "password": "test"
}
```

### Response Format
```json
{
    "status": "01",
    "message": "Successful!",
    "data": [
        {
            "id": 1625441,
            "name": "Cháo gói, cháo tươi",
            "code": "MMCP_chaog",
            "icon": "https://...",
            "parent_id": 1625378,
            "description": null,
            "published": 1
        }
    ]
}
```

## 📊 Features chi tiết

### 🔍 **Search & Filter**
- **Real-time search**: Gõ để filter ngay lập tức
- **Multi-column search**: Tìm trong tất cả columns
- **Case-insensitive**: Không phân biệt hoa thường
- **Instant results**: Kết quả hiển thị ngay

### 📤 **Export Options**

#### **CSV Export**
- **Auto filename**: `vipomart_data_YYYYMMDD_HHMMSS.csv`
- **UTF-8 encoding**: Hỗ trợ tiếng Việt
- **Headers included**: Có tên columns
- **Filtered data**: Chỉ export data đã filter

#### **Clipboard Copy**
- **Tab-separated format**: Sẵn sàng paste vào Excel
- **Headers included**: Có tên columns
- **All data types**: String, number, null values
- **Ready for Excel**: Paste trực tiếp vào spreadsheet

### 🔍 **Detail View**
- **Double-click**: Mở cửa sổ chi tiết
- **Full data**: Hiển thị đầy đủ nội dung
- **Formatted display**: Key-value pairs rõ ràng
- **Scrollable**: Với scrollbar cho data dài

## 🛠️ Technical Details

### Dependencies
```bash
# System requirements
python3 >= 3.6
python3-tk
requests
```

### Error Handling
- **Connection timeout**: 30 seconds
- **Network errors**: Connection error detection
- **API errors**: Status code và message parsing
- **Input validation**: Empty fields detection
- **User feedback**: Clear error messages

### Threading
- **Non-blocking UI**: API calls trong separate thread
- **Progress indication**: Progress bar during calls
- **Button states**: Disable/enable during operations
- **Safe UI updates**: Thread-safe UI updates

## 💡 Tips sử dụng

### Encoded SQL Examples
```
# Sample encoded SQL (for testing)
dGVzdF9lbmNyeXB0ZWRfc3FsX2RhdGFfaGVyZS4uLg==
```

### Best Practices
1. **Test với sample**: Click "📝 Load Sample" để test
2. **Copy encoded SQL**: Backup encoded SQL trước khi call
3. **Use search**: Filter large datasets với search
4. **Export results**: Save kết quả quan trọng
5. **Check status**: Đọc status bar để biết kết quả

### Keyboard Workflow
```
1. Ctrl+V     → Paste encoded SQL
2. Tab        → Move to password (if needed)
3. Ctrl+Enter → Call API
4. Type       → Search in results
5. Double-click → View details
```

## 🐛 Troubleshooting

### Common Issues

#### **"No data in clipboard"**
- Copy encoded SQL trước khi paste
- Kiểm tra clipboard có data

#### **"Request timeout"**
- Kiểm tra internet connection
- Thử lại với encoded SQL khác
- Server có thể busy

#### **"API Error: 400/500"**
- Kiểm tra encoded SQL format
- Kiểm tra password đúng
- Thử với sample encoded SQL

#### **"Connection error"**
- Kiểm tra internet connection
- Kiểm tra firewall settings
- Thử restart app

### Debug Steps
1. **Test với sample**: Click "📝 Load Sample"
2. **Check password**: Mặc định "test"
3. **Verify encoded SQL**: Phải là base64 valid
4. **Check internet**: Ping google.com
5. **Restart app**: Close và mở lại

## 🚧 Roadmap

- [ ] **Multiple API endpoints** support
- [ ] **Request history** với cache
- [ ] **Custom headers** editor
- [ ] **Batch processing** multiple encoded SQLs
- [ ] **Data visualization** charts
- [ ] **Query performance** metrics
- [ ] **Auto-refresh** results
- [ ] **Dark theme** support

## 📄 License

MIT License - Sử dụng tự do.

---

**🎯 Focus on results - Enter encoded SQL and get data instantly! 🚀**
