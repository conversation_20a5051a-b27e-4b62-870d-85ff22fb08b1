#!/bin/bash

echo "🔗 Starting Minimal API GUI..."

# Kill any existing instances
pkill -f "python3.*minimal_api" 2>/dev/null
pkill -f "python3.*direct_api" 2>/dev/null

# Check Python3
if ! which python3 > /dev/null 2>&1; then
    echo "❌ Python3 not installed!"
    echo "Run: sudo apt install python3"
    exit 1
fi

# Check tkinter
if ! python3 -c "import tkinter" > /dev/null 2>&1; then
    echo "❌ tkinter not installed!"
    echo "Run: sudo apt install python3-tk"
    exit 1
fi

# Check and install dependencies
echo "📦 Checking dependencies..."

if ! python3 -c "import requests" > /dev/null 2>&1; then
    echo "📦 Installing requests..."
    pip3 install requests
fi

echo "✅ Dependencies OK"
echo "🚀 Launching Minimal GUI..."
echo ""
echo "💡 Simple workflow:"
echo "   1. Enter encoded SQL (or click 'Load Sample')"
echo "   2. Set password (default: 'test')"
echo "   3. Click 'Call API' or press Ctrl+Enter"
echo "   4. View results and export if needed"
echo ""

# Run the app
python3 minimal_api_gui.py
