#!/usr/bin/env python3
"""
Ứng dụng ghi chú giống Notion
Tác giả: Assistant
<PERSON><PERSON><PERSON>: 1.0
"""

import tkinter as tk
from tkinter import ttk, messagebox, font
import json
import os
from datetime import datetime
import uuid

class NotionApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Notion Clone - Ung dung ghi chu")
        self.root.geometry("1200x800")
        self.root.minsize(800, 600)
        
        # Đường dẫn file lưu trữ
        self.data_file = os.path.expanduser("~/.notion_app_data.json")
        
        # Dữ liệu
        self.pages = []
        self.current_page = None
        
        # Fonts (simplified to avoid X11 errors)
        self.title_font = ("Arial", 24, "bold")
        self.heading_font = ("Arial", 16, "bold")
        self.normal_font = ("Arial", 11)
        
        # Colors (Notion-like)
        self.bg_color = "#ffffff"
        self.sidebar_color = "#f7f6f3"
        self.text_color = "#37352f"
        self.gray_color = "#9b9a97"
        self.blue_color = "#2383e2"
        
        # Tạo giao diện
        self.create_widgets()
        
        # Tải dữ liệu
        self.load_data()
        
        # Cập nhật sidebar
        self.refresh_sidebar()
        
    def create_widgets(self):
        """Tạo giao diện giống Notion"""
        
        # Main container
        main_container = ttk.Frame(self.root)
        main_container.pack(fill=tk.BOTH, expand=True)
        
        # Sidebar (bên trái)
        self.create_sidebar(main_container)
        
        # Main content area (bên phải)
        self.create_main_area(main_container)
        
    def create_sidebar(self, parent):
        """Tạo sidebar giống Notion"""
        
        # Sidebar frame
        sidebar_frame = tk.Frame(parent, bg=self.sidebar_color, width=250)
        sidebar_frame.pack(side=tk.LEFT, fill=tk.Y)
        sidebar_frame.pack_propagate(False)
        
        # Header sidebar
        header_frame = tk.Frame(sidebar_frame, bg=self.sidebar_color)
        header_frame.pack(fill=tk.X, padx=15, pady=15)
        
        title_label = tk.Label(header_frame, text="My Notion",
                              font=self.heading_font, bg=self.sidebar_color,
                              fg=self.text_color)
        title_label.pack(anchor=tk.W)
        
        # New page button
        new_page_btn = tk.Button(header_frame, text="+ New Page",
                                font=self.normal_font, bg=self.blue_color,
                                fg="white", relief=tk.FLAT, padx=15, pady=5,
                                command=self.create_new_page)
        new_page_btn.pack(anchor=tk.W, pady=(10, 0))
        
        # Pages list
        pages_label = tk.Label(sidebar_frame, text="Pages", 
                              font=("Arial", 10, "bold"), bg=self.sidebar_color,
                              fg=self.gray_color)
        pages_label.pack(anchor=tk.W, padx=15, pady=(20, 5))
        
        # Scrollable pages list
        pages_canvas = tk.Canvas(sidebar_frame, bg=self.sidebar_color, highlightthickness=0)
        pages_scrollbar = ttk.Scrollbar(sidebar_frame, orient="vertical", command=pages_canvas.yview)
        self.pages_frame = tk.Frame(pages_canvas, bg=self.sidebar_color)
        
        self.pages_frame.bind(
            "<Configure>",
            lambda e: pages_canvas.configure(scrollregion=pages_canvas.bbox("all"))
        )
        
        pages_canvas.create_window((0, 0), window=self.pages_frame, anchor="nw")
        pages_canvas.configure(yscrollcommand=pages_scrollbar.set)
        
        pages_canvas.pack(side="left", fill="both", expand=True, padx=15)
        pages_scrollbar.pack(side="right", fill="y")
        
    def create_main_area(self, parent):
        """Tạo khu vực nội dung chính"""
        
        # Main content frame
        self.main_frame = tk.Frame(parent, bg=self.bg_color)
        self.main_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # Welcome screen
        self.create_welcome_screen()
        
    def create_welcome_screen(self):
        """Tạo màn hình chào mừng"""
        
        # Clear main frame
        for widget in self.main_frame.winfo_children():
            widget.destroy()
            
        welcome_frame = tk.Frame(self.main_frame, bg=self.bg_color)
        welcome_frame.pack(expand=True, fill=tk.BOTH)
        
        # Center content
        center_frame = tk.Frame(welcome_frame, bg=self.bg_color)
        center_frame.place(relx=0.5, rely=0.5, anchor=tk.CENTER)
        
        welcome_label = tk.Label(center_frame, text="Welcome to Notion Clone!",
                                font=self.title_font, bg=self.bg_color,
                                fg=self.text_color)
        welcome_label.pack(pady=20)
        
        subtitle_label = tk.Label(center_frame, text="Create your first page to get started",
                                 font=self.normal_font, bg=self.bg_color,
                                 fg=self.gray_color)
        subtitle_label.pack()
        
        create_btn = tk.Button(center_frame, text="Create New Page",
                              font=self.normal_font, bg=self.blue_color,
                              fg="white", relief=tk.FLAT, padx=20, pady=10,
                              command=self.create_new_page)
        create_btn.pack(pady=20)
        
    def create_page_editor(self, page):
        """Tạo editor cho page"""
        
        # Clear main frame
        for widget in self.main_frame.winfo_children():
            widget.destroy()
            
        # Editor frame
        editor_frame = tk.Frame(self.main_frame, bg=self.bg_color)
        editor_frame.pack(fill=tk.BOTH, expand=True, padx=50, pady=30)
        
        # Page title
        title_frame = tk.Frame(editor_frame, bg=self.bg_color)
        title_frame.pack(fill=tk.X, pady=(0, 20))
        
        self.title_entry = tk.Entry(title_frame, font=self.title_font,
                                   bg=self.bg_color, fg=self.text_color,
                                   relief=tk.FLAT, insertbackground=self.text_color)
        self.title_entry.pack(fill=tk.X)
        self.title_entry.insert(0, page.get('title', 'Untitled'))
        self.title_entry.bind('<KeyRelease>', self.on_title_change)
        
        # Toolbar
        toolbar_frame = tk.Frame(editor_frame, bg=self.bg_color)
        toolbar_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Toolbar buttons
        tk.Button(toolbar_frame, text="H1", font=("Arial", 10, "bold"),
                 command=lambda: self.insert_block("heading1")).pack(side=tk.LEFT, padx=2)
        tk.Button(toolbar_frame, text="H2", font=("Arial", 9, "bold"),
                 command=lambda: self.insert_block("heading2")).pack(side=tk.LEFT, padx=2)
        tk.Button(toolbar_frame, text="• List", font=self.normal_font,
                 command=lambda: self.insert_block("bullet")).pack(side=tk.LEFT, padx=2)
        tk.Button(toolbar_frame, text="1. List", font=self.normal_font,
                 command=lambda: self.insert_block("numbered")).pack(side=tk.LEFT, padx=2)
        tk.Button(toolbar_frame, text="Quote", font=self.normal_font,
                 command=lambda: self.insert_block("quote")).pack(side=tk.LEFT, padx=2)
        tk.Button(toolbar_frame, text="Code", font=("Courier", 10),
                 command=lambda: self.insert_block("code")).pack(side=tk.LEFT, padx=2)
        
        # Content area with scrollbar
        content_frame = tk.Frame(editor_frame, bg=self.bg_color)
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        # Text widget for content
        self.content_text = tk.Text(content_frame, font=self.normal_font,
                                   bg=self.bg_color, fg=self.text_color,
                                   relief=tk.FLAT, wrap=tk.WORD,
                                   insertbackground=self.text_color,
                                   selectbackground=self.blue_color,
                                   selectforeground="white")
        
        content_scrollbar = ttk.Scrollbar(content_frame, orient=tk.VERTICAL,
                                         command=self.content_text.yview)
        self.content_text.configure(yscrollcommand=content_scrollbar.set)
        
        self.content_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        content_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Load content
        self.content_text.insert("1.0", page.get('content', ''))
        
        # Bind events
        self.content_text.bind('<KeyRelease>', self.on_content_change)
        self.content_text.bind('<Key>', self.on_key_press)

        # Auto-save timer
        self.auto_save_timer = None
        
        # Configure text tags for styling
        self.setup_text_tags()
        
        # Bottom toolbar
        bottom_frame = tk.Frame(editor_frame, bg=self.bg_color)
        bottom_frame.pack(fill=tk.X, pady=(10, 0))
        
        # Page info
        page_info = f"Created: {page.get('created_date', 'Unknown')} | Modified: {page.get('modified_date', 'Unknown')}"
        info_label = tk.Label(bottom_frame, text=page_info, font=("Arial", 9),
                             bg=self.bg_color, fg=self.gray_color)
        info_label.pack(side=tk.LEFT)
        
        # Delete button
        delete_btn = tk.Button(bottom_frame, text="Delete Page", font=self.normal_font,
                              bg="#e03e3e", fg="white", relief=tk.FLAT,
                              command=self.delete_current_page)
        delete_btn.pack(side=tk.RIGHT)
        
    def setup_text_tags(self):
        """Thiết lập các tag để format text"""
        
        self.content_text.tag_configure("heading1", font=("Arial", 20, "bold"), spacing1=10, spacing3=5)
        self.content_text.tag_configure("heading2", font=("Arial", 16, "bold"), spacing1=8, spacing3=4)
        self.content_text.tag_configure("bullet", lmargin1=20, lmargin2=40)
        self.content_text.tag_configure("numbered", lmargin1=20, lmargin2=40)
        self.content_text.tag_configure("quote", lmargin1=20, lmargin2=20, 
                                       background="#f0f0f0", relief=tk.RAISED, borderwidth=1)
        self.content_text.tag_configure("code", font=("Courier", 10), 
                                       background="#f5f5f5", relief=tk.RAISED, borderwidth=1)
        
    def insert_block(self, block_type):
        """Chèn block theo kiểu Notion"""
        
        cursor_pos = self.content_text.index(tk.INSERT)
        
        if block_type == "heading1":
            self.content_text.insert(cursor_pos, "\n# Heading 1\n")
            # Apply tag
            start_line = int(cursor_pos.split('.')[0]) + 1
            self.content_text.tag_add("heading1", f"{start_line}.0", f"{start_line}.end")
            
        elif block_type == "heading2":
            self.content_text.insert(cursor_pos, "\n## Heading 2\n")
            start_line = int(cursor_pos.split('.')[0]) + 1
            self.content_text.tag_add("heading2", f"{start_line}.0", f"{start_line}.end")
            
        elif block_type == "bullet":
            self.content_text.insert(cursor_pos, "\n• Bullet point\n")
            start_line = int(cursor_pos.split('.')[0]) + 1
            self.content_text.tag_add("bullet", f"{start_line}.0", f"{start_line}.end")
            
        elif block_type == "numbered":
            self.content_text.insert(cursor_pos, "\n1. Numbered item\n")
            start_line = int(cursor_pos.split('.')[0]) + 1
            self.content_text.tag_add("numbered", f"{start_line}.0", f"{start_line}.end")
            
        elif block_type == "quote":
            self.content_text.insert(cursor_pos, "\n> Quote text\n")
            start_line = int(cursor_pos.split('.')[0]) + 1
            self.content_text.tag_add("quote", f"{start_line}.0", f"{start_line}.end")
            
        elif block_type == "code":
            self.content_text.insert(cursor_pos, "\n```\nCode block\n```\n")
            start_line = int(cursor_pos.split('.')[0]) + 1
            self.content_text.tag_add("code", f"{start_line}.0", f"{start_line+2}.end")
            
    def create_new_page(self):
        """Tạo page mới"""
        
        new_page = {
            'id': str(uuid.uuid4()),
            'title': 'Untitled',
            'content': '',
            'created_date': self.get_current_time(),
            'modified_date': self.get_current_time()
        }
        
        self.pages.append(new_page)
        self.current_page = new_page
        self.save_data()
        self.refresh_sidebar()
        self.create_page_editor(new_page)
        
    def on_title_change(self, event=None):
        """Xử lý khi thay đổi title"""
        if self.current_page:
            new_title = self.title_entry.get().strip()
            if new_title:
                self.current_page['title'] = new_title
            else:
                self.current_page['title'] = 'Untitled'
            self.current_page['modified_date'] = self.get_current_time()
            self.save_data()
            self.refresh_sidebar()
            
    def on_content_change(self, event=None):
        """Xử lý khi thay đổi content"""
        if self.current_page:
            # Cancel previous timer
            if self.auto_save_timer:
                self.root.after_cancel(self.auto_save_timer)

            # Set new timer for auto-save (delay 1 second)
            self.auto_save_timer = self.root.after(1000, self.auto_save_content)

    def auto_save_content(self):
        """Auto-save content"""
        if self.current_page:
            self.current_page['content'] = self.content_text.get("1.0", tk.END).strip()
            self.current_page['modified_date'] = self.get_current_time()
            self.save_data()

    def on_key_press(self, event):
        """Xử lý phím tắt giống Notion"""

        # Ctrl+B for bold (placeholder)
        if event.state & 0x4 and event.keysym == 'b':
            return "break"

        # Slash commands
        if event.char == '/':
            self.show_slash_menu()
            return "break"

        # Enter key behaviors
        if event.keysym == 'Return':
            current_line = self.content_text.get("insert linestart", "insert")

            # Auto-continue lists
            if current_line.strip().startswith('•'):
                self.content_text.insert("insert", "\n• ")
                return "break"
            elif current_line.strip().startswith(('1.', '2.', '3.', '4.', '5.')):
                # Get number and increment
                try:
                    num = int(current_line.strip().split('.')[0]) + 1
                    self.content_text.insert("insert", f"\n{num}. ")
                    return "break"
                except:
                    pass

    def show_slash_menu(self):
        """Hiển thị menu slash commands"""

        # Create popup menu
        slash_menu = tk.Menu(self.root, tearoff=0)
        slash_menu.add_command(label="📝 Text", command=lambda: self.insert_slash_block("text"))
        slash_menu.add_command(label="# Heading 1", command=lambda: self.insert_slash_block("h1"))
        slash_menu.add_command(label="## Heading 2", command=lambda: self.insert_slash_block("h2"))
        slash_menu.add_command(label="• Bullet List", command=lambda: self.insert_slash_block("bullet"))
        slash_menu.add_command(label="1. Numbered List", command=lambda: self.insert_slash_block("numbered"))
        slash_menu.add_command(label="💬 Quote", command=lambda: self.insert_slash_block("quote"))
        slash_menu.add_command(label="💻 Code", command=lambda: self.insert_slash_block("code"))
        slash_menu.add_command(label="📋 To-do", command=lambda: self.insert_slash_block("todo"))

        # Show menu at cursor position
        try:
            x, y, _, _ = self.content_text.bbox("insert")
            x += self.content_text.winfo_rootx()
            y += self.content_text.winfo_rooty() + 20
            slash_menu.post(x, y)
        except:
            pass

    def insert_slash_block(self, block_type):
        """Chèn block từ slash command"""

        # Remove the '/' character
        current_pos = self.content_text.index("insert")
        line_start = self.content_text.index("insert linestart")
        line_content = self.content_text.get(line_start, current_pos)

        if line_content.endswith('/'):
            self.content_text.delete(f"{current_pos}-1c", current_pos)

        if block_type == "text":
            self.content_text.insert("insert", "Type something...")
        elif block_type == "h1":
            self.content_text.insert("insert", "# Heading 1")
        elif block_type == "h2":
            self.content_text.insert("insert", "## Heading 2")
        elif block_type == "bullet":
            self.content_text.insert("insert", "• Bullet point")
        elif block_type == "numbered":
            self.content_text.insert("insert", "1. Numbered item")
        elif block_type == "quote":
            self.content_text.insert("insert", "> Quote text")
        elif block_type == "code":
            self.content_text.insert("insert", "```\nCode here\n```")
        elif block_type == "todo":
            self.content_text.insert("insert", "☐ To-do item")
            
    def delete_current_page(self):
        """Xóa page hiện tại"""
        if self.current_page and messagebox.askyesno("Confirm Delete", 
                                                    f"Delete page '{self.current_page['title']}'?"):
            self.pages = [p for p in self.pages if p['id'] != self.current_page['id']]
            self.current_page = None
            self.save_data()
            self.refresh_sidebar()
            self.create_welcome_screen()
            
    def open_page(self, page):
        """Mở một page"""
        self.current_page = page
        self.create_page_editor(page)
        
    def refresh_sidebar(self):
        """Cập nhật sidebar"""
        
        # Clear pages frame
        for widget in self.pages_frame.winfo_children():
            widget.destroy()
            
        # Add pages
        for page in self.pages:
            page_btn = tk.Button(self.pages_frame, text=f"[P] {page['title'][:30]}",
                               font=self.normal_font, bg=self.sidebar_color,
                               fg=self.text_color, relief=tk.FLAT, anchor=tk.W,
                               command=lambda p=page: self.open_page(p))
            page_btn.pack(fill=tk.X, pady=2, padx=5)
            
            # Highlight current page
            if self.current_page and page['id'] == self.current_page['id']:
                page_btn.configure(bg=self.blue_color, fg="white")
                
    def load_data(self):
        """Tải dữ liệu từ file"""
        try:
            if os.path.exists(self.data_file):
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    self.pages = json.load(f)
        except Exception as e:
            messagebox.showerror("Error", f"Cannot load data: {str(e)}")
            self.pages = []
            
    def save_data(self):
        """Lưu dữ liệu vào file"""
        try:
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(self.pages, f, ensure_ascii=False, indent=2)
        except Exception as e:
            messagebox.showerror("Error", f"Cannot save data: {str(e)}")
            
    def get_current_time(self):
        """Lấy thời gian hiện tại"""
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")


def main():
    """Hàm main"""
    root = tk.Tk()
    app = NotionApp(root)
    
    def on_closing():
        if messagebox.askokcancel("Exit", "Do you want to exit?"):
            root.destroy()
    
    root.protocol("WM_DELETE_WINDOW", on_closing)
    root.mainloop()


if __name__ == "__main__":
    main()
