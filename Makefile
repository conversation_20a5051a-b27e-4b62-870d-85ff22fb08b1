# Makefile cho Notes App

.PHONY: help install run build-deb clean test

help:
	@echo "📝 Notes App - Makefile Commands"
	@echo ""
	@echo "Available commands:"
	@echo "  make install     - Cài đặt dependencies"
	@echo "  make run         - Chạy ứng dụng"
	@echo "  make build-deb   - Build file .deb"
	@echo "  make clean       - Dọn dẹp build files"
	@echo "  make test        - Chạy tests (nếu có)"
	@echo "  make help        - Hiển thị help này"

install:
	@echo "📦 Cài đặt dependencies..."
	sudo apt update
	sudo apt install -y python3 python3-pip python3-tk python3-stdeb dh-python
	@echo "✅ Cài đặt hoàn tất!"

run:
	@echo "🚀 Chạy Notes App..."
	python3 notes_app.py

build-deb:
	@echo "🔨 Build file .deb..."
	./build_deb.sh

clean:
	@echo "🧹 Dọn dẹp build files..."
	rm -rf build/
	rm -rf dist/
	rm -rf deb_dist/
	rm -rf *.egg-info/
	find . -name "*.pyc" -delete
	find . -name "__pycache__" -type d -exec rm -rf {} +
	@echo "✅ Dọn dẹp hoàn tất!"

test:
	@echo "🧪 Chạy tests..."
	python3 -m py_compile notes_app.py
	@echo "✅ Syntax check passed!"

# Shortcut commands
deb: build-deb
start: run
