#!/bin/bash

# Script để chạy Notion Clone App
echo "🚀 Khởi động Notion Clone..."

# Kiểm tra Python3 và tkinter
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 chưa được cài đặt!"
    echo "Chạy: sudo apt install python3"
    exit 1
fi

if ! python3 -c "import tkinter" &> /dev/null; then
    echo "❌ tkinter chưa được cài đặt!"
    echo "Chạy: sudo apt install python3-tk"
    exit 1
fi

# Dừng ứng dụng cũ nếu đang chạy
pkill -f "python3 notes_app.py" 2>/dev/null
pkill -f "python3 notion_app.py" 2>/dev/null

echo "📝 Chạy Notion Clone App..."
# Chạy ứng dụng
python3 notion_app.py
