#!/usr/bin/env python3
"""
Integrated App - SQL Encoder + API Caller
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import requests
import json
import csv
from datetime import datetime
import threading
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.backends import default_backend
import base64
import secrets

class IntegratedApp:
    def __init__(self, root):
        self.root = root
        self.root.title("SQL Encoder + API Caller - Vipomart")
        self.root.geometry("1400x900")
        self.root.minsize(1000, 700)
        
        # Encryption constants
        self.GCM_TAG_LENGTH = 16
        self.GCM_IV_LENGTH = 12
        self.ITERATION_COUNT = 65536
        self.KEY_LENGTH = 32
        self.SALT_LENGTH = 16
        
        # API Configuration
        self.api_url = "https://api.vipomart.vn/authen/query"
        self.headers = {
            'X-Internal-Secret': 'Nv82Lx7Kq!Dfj29slPq2R5mxVfLzXp9A',
            'User-Agent': 'Apidog/1.0.0 (https://apidog.com)',
            'Content-Type': 'application/json',
            'Cookie': 'JSESSIONID=F17B9DB321617A561FEC4966FDEFDBD3; SERVERID=A; JSESSIONID=D61C911FBB86F7CCF49CEF37540D0877; JSESSIONID=AA2727DFD8B4C62D662DA0CEEE4513D6; JSESSIONID=E99659309A054438C6D8C3D0DFC5848B'
        }
        
        # Data storage
        self.current_data = []
        self.filtered_data = []
        
        self.create_widgets()
        
    def create_widgets(self):
        """Tạo giao diện tích hợp"""
        
        # Main container
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        title_label = ttk.Label(main_frame, text="🔐 SQL Encoder + API Caller - Vipomart", 
                               font=("Arial", 18, "bold"))
        title_label.pack(pady=(0, 20))
        
        # Create notebook for tabs
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # Tab 1: SQL Encoder
        self.create_encoder_tab()
        
        # Tab 2: API Caller
        self.create_api_tab()
        
        # Status bar (shared)
        self.status_var = tk.StringVar(value="Ready")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.pack(fill=tk.X, pady=(10, 0))
        
    def create_encoder_tab(self):
        """Tạo tab SQL Encoder"""
        
        encoder_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(encoder_frame, text="🔐 SQL Encoder")
        
        # Password section
        password_frame = ttk.LabelFrame(encoder_frame, text="Password", padding="10")
        password_frame.pack(fill=tk.X, pady=(0, 10))
        
        password_input_frame = ttk.Frame(password_frame)
        password_input_frame.pack(fill=tk.X)
        
        ttk.Label(password_input_frame, text="Password:").pack(side=tk.LEFT)
        self.password_var = tk.StringVar(value="test")
        self.password_entry = ttk.Entry(password_input_frame, textvariable=self.password_var, 
                                       show="*", width=20, font=("Arial", 12))
        self.password_entry.pack(side=tk.LEFT, padx=(10, 0))
        
        # Show/Hide password
        self.show_password_var = tk.BooleanVar()
        show_password_cb = ttk.Checkbutton(password_input_frame, text="Show", 
                                          variable=self.show_password_var,
                                          command=self.toggle_password)
        show_password_cb.pack(side=tk.LEFT, padx=(10, 0))
        
        # SQL Input section
        sql_frame = ttk.LabelFrame(encoder_frame, text="SQL Query Input", padding="10")
        sql_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # SQL text area
        sql_text_frame = ttk.Frame(sql_frame)
        sql_text_frame.pack(fill=tk.BOTH, expand=True)
        
        self.sql_text = tk.Text(sql_text_frame, wrap=tk.WORD, font=("Courier", 11))
        sql_scrollbar = ttk.Scrollbar(sql_text_frame, orient=tk.VERTICAL, 
                                     command=self.sql_text.yview)
        self.sql_text.configure(yscrollcommand=sql_scrollbar.set)
        
        self.sql_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        sql_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Control buttons
        control_frame = ttk.Frame(sql_frame)
        control_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(control_frame, text="Load Sample SQL", 
                  command=self.load_sample_sql).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(control_frame, text="🔐 Encrypt SQL", 
                  command=self.encrypt_sql).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(control_frame, text="🚀 Encrypt & Call API", 
                  command=self.encrypt_and_call_api).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(control_frame, text="📋 Copy Encoded", 
                  command=self.copy_encoded, state=tk.DISABLED).pack(side=tk.RIGHT)
        
        # Result section
        result_frame = ttk.LabelFrame(encoder_frame, text="Encoded SQL Result", padding="10")
        result_frame.pack(fill=tk.BOTH, expand=True)
        
        # Result text area
        result_text_frame = ttk.Frame(result_frame)
        result_text_frame.pack(fill=tk.BOTH, expand=True)
        
        self.encoded_text = tk.Text(result_text_frame, wrap=tk.WORD, font=("Courier", 10),
                                   state=tk.DISABLED)
        encoded_scrollbar = ttk.Scrollbar(result_text_frame, orient=tk.VERTICAL, 
                                         command=self.encoded_text.yview)
        self.encoded_text.configure(yscrollcommand=encoded_scrollbar.set)
        
        self.encoded_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        encoded_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
    def create_api_tab(self):
        """Tạo tab API Caller"""
        
        api_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(api_frame, text="🔗 API Caller")
        
        # Input section
        input_frame = ttk.LabelFrame(api_frame, text="API Input", padding="10")
        input_frame.pack(fill=tk.X, pady=(0, 10))
        
        # EncodedSql input
        ttk.Label(input_frame, text="Encoded SQL:").pack(anchor=tk.W)
        
        encoded_input_frame = ttk.Frame(input_frame)
        encoded_input_frame.pack(fill=tk.X, pady=(5, 10))
        
        self.api_sql_text = tk.Text(encoded_input_frame, height=3, wrap=tk.WORD, font=("Courier", 10))
        api_sql_scrollbar = ttk.Scrollbar(encoded_input_frame, orient=tk.VERTICAL, 
                                         command=self.api_sql_text.yview)
        self.api_sql_text.configure(yscrollcommand=api_sql_scrollbar.set)
        
        self.api_sql_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        api_sql_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Control buttons
        api_control_frame = ttk.Frame(input_frame)
        api_control_frame.pack(fill=tk.X)
        
        ttk.Button(api_control_frame, text="🔗 Call API", 
                  command=self.call_api).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(api_control_frame, text="📋 Paste from Encoder", 
                  command=self.paste_from_encoder).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(api_control_frame, text="🗑️ Clear", 
                  command=self.clear_api_data).pack(side=tk.LEFT)
        
        # Export buttons
        ttk.Button(api_control_frame, text="📊 Export CSV", 
                  command=self.export_csv, state=tk.DISABLED).pack(side=tk.RIGHT, padx=(10, 0))
        
        ttk.Button(api_control_frame, text="📋 Copy Data", 
                  command=self.copy_data, state=tk.DISABLED).pack(side=tk.RIGHT)
        
        # Results section
        results_frame = ttk.LabelFrame(api_frame, text="API Results", padding="10")
        results_frame.pack(fill=tk.BOTH, expand=True)
        
        # Search bar
        search_frame = ttk.Frame(results_frame)
        search_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(search_frame, text="Search:").pack(side=tk.LEFT)
        self.search_var = tk.StringVar()
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var, width=30)
        search_entry.pack(side=tk.LEFT, padx=(10, 0))
        search_entry.bind('<KeyRelease>', self.on_search)
        
        # Results info
        self.info_var = tk.StringVar(value="No data")
        info_label = ttk.Label(search_frame, textvariable=self.info_var)
        info_label.pack(side=tk.RIGHT)
        
        # Progress bar
        self.progress = ttk.Progressbar(results_frame, mode='indeterminate')
        self.progress.pack(fill=tk.X, pady=(0, 10))
        
        # Treeview for results
        tree_frame = ttk.Frame(results_frame)
        tree_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create treeview with scrollbars
        self.tree = ttk.Treeview(tree_frame, show="headings")
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.tree.yview)
        h_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        self.tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Pack treeview and scrollbars
        self.tree.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")
        
        # Configure grid weights
        tree_frame.grid_rowconfigure(0, weight=1)
        tree_frame.grid_columnconfigure(0, weight=1)
        
        # Bind double click
        self.tree.bind("<Double-1>", self.on_item_double_click)
        
    def toggle_password(self):
        """Toggle hiển thị password"""
        if self.show_password_var.get():
            self.password_entry.configure(show='')
        else:
            self.password_entry.configure(show='*')
            
    def load_sample_sql(self):
        """Load SQL mẫu"""
        sample_sql = """SELECT 
    c.id,
    c.name,
    c.code,
    c.icon,
    c.parent_id,
    c.description,
    c.published
FROM categories c 
WHERE c.published = 1 
    AND c.parent_id IS NOT NULL
ORDER BY c.name ASC
LIMIT 10"""
        
        self.sql_text.delete("1.0", tk.END)
        self.sql_text.insert("1.0", sample_sql)
        self.status_var.set("Sample SQL loaded")
        
    def generate_salt(self):
        """Tạo salt ngẫu nhiên"""
        return secrets.token_bytes(self.SALT_LENGTH)
        
    def generate_iv(self):
        """Tạo IV ngẫu nhiên"""
        return secrets.token_bytes(self.GCM_IV_LENGTH)
        
    def derive_key_from_password(self, password, salt):
        """Tạo key từ password và salt sử dụng PBKDF2"""
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=self.KEY_LENGTH,
            salt=salt,
            iterations=self.ITERATION_COUNT,
            backend=default_backend()
        )
        return kdf.derive(password.encode('utf-8'))
        
    def encrypt_aes_gcm(self, plaintext, password):
        """Mã hóa text sử dụng AES/GCM"""
        try:
            # Generate random salt and IV
            salt = self.generate_salt()
            iv = self.generate_iv()
            
            # Derive key from password
            key = self.derive_key_from_password(password, salt)
            
            # Create cipher
            cipher = Cipher(
                algorithms.AES(key),
                modes.GCM(iv),
                backend=default_backend()
            )
            encryptor = cipher.encryptor()
            
            # Encrypt
            ciphertext = encryptor.update(plaintext.encode('utf-8')) + encryptor.finalize()
            
            # Get authentication tag
            tag = encryptor.tag
            
            # Combine salt + iv + ciphertext + tag
            combined = salt + iv + ciphertext + tag
            
            # Encode to base64
            return base64.b64encode(combined).decode('utf-8')
            
        except Exception as e:
            raise Exception(f"Encryption failed: {str(e)}")
            
    def encrypt_sql(self):
        """Mã hóa SQL"""
        
        sql_query = self.sql_text.get("1.0", tk.END).strip()
        password = self.password_var.get().strip()
        
        if not sql_query:
            messagebox.showwarning("Warning", "Please enter SQL query!")
            return None
            
        if not password:
            messagebox.showwarning("Warning", "Please enter password!")
            return None
            
        try:
            # Encrypt SQL
            self.status_var.set("Encrypting...")
            self.root.update()
            
            encoded_sql = self.encrypt_aes_gcm(sql_query, password)
            
            # Display result
            self.encoded_text.configure(state=tk.NORMAL)
            self.encoded_text.delete("1.0", tk.END)
            self.encoded_text.insert("1.0", encoded_sql)
            self.encoded_text.configure(state=tk.DISABLED)
            
            # Enable copy button
            for widget in self.notebook.winfo_children():
                for child in widget.winfo_children():
                    if isinstance(child, ttk.LabelFrame):
                        for grandchild in child.winfo_children():
                            if isinstance(grandchild, ttk.Frame):
                                for button in grandchild.winfo_children():
                                    if isinstance(button, ttk.Button) and "Copy Encoded" in button.cget('text'):
                                        button.configure(state=tk.NORMAL)
            
            self.status_var.set(f"Encryption successful! Length: {len(encoded_sql)} characters")
            return encoded_sql
            
        except Exception as e:
            messagebox.showerror("Error", f"Encryption failed: {str(e)}")
            self.status_var.set("Encryption failed")
            return None

    def copy_encoded(self):
        """Copy encoded SQL vào clipboard"""

        result = self.encoded_text.get("1.0", tk.END).strip()
        if result:
            self.root.clipboard_clear()
            self.root.clipboard_append(result)
            messagebox.showinfo("Success", "Encoded SQL copied to clipboard!")
            self.status_var.set("Copied to clipboard")
        else:
            messagebox.showwarning("Warning", "No encoded SQL to copy!")

    def encrypt_and_call_api(self):
        """Mã hóa SQL và gọi API luôn"""

        encoded_sql = self.encrypt_sql()
        if encoded_sql:
            # Switch to API tab
            self.notebook.select(1)

            # Paste encoded SQL to API tab
            self.api_sql_text.delete("1.0", tk.END)
            self.api_sql_text.insert("1.0", encoded_sql)

            # Call API
            self.call_api()

    def paste_from_encoder(self):
        """Paste encoded SQL từ encoder tab"""

        encoded_sql = self.encoded_text.get("1.0", tk.END).strip()
        if encoded_sql:
            self.api_sql_text.delete("1.0", tk.END)
            self.api_sql_text.insert("1.0", encoded_sql)
            self.status_var.set("Pasted from encoder")
        else:
            messagebox.showwarning("Warning", "No encoded SQL to paste! Please encrypt SQL first.")


def main():
    """Main function"""
    root = tk.Tk()
    app = IntegratedApp(root)

    def on_closing():
        if messagebox.askokcancel("Exit", "Exit application?"):
            root.destroy()

    root.protocol("WM_DELETE_WINDOW", on_closing)
    root.mainloop()


if __name__ == "__main__":
    main()
