# 📝 Simple Notion Clone

Ứng dụng ghi chú đơn giản lấy cảm hứng từ Notion, được tối ưu để tránh lỗi X11 và chạy ổn định trên Ubuntu.

## ✨ Tính năng

### 📄 Quản lý Pages
- ➕ **Tạo page mới** với dialog nhập tên
- 📝 **Chỉnh sửa title** trực tiếp
- 📋 **Sidebar** hiển thị danh sách pages
- 🗑️ **Xóa page** với xác nhận
- 💾 **Auto-save** khi thay đổi nội dung

### ✏️ Editor Features
- 📝 **Rich text area** với scrollbar
- 🔧 **Toolbar** với các nút format nhanh:
  - **H1** - Thêm `# ` (Heading 1)
  - **H2** - Thêm `## ` (Heading 2)  
  - **List** - Thêm `- ` (Bullet list)
  - **Quote** - Thêm `> ` (Quote)
  - **Code** - Thêm code block
  - **Todo** - Thêm `- [ ] ` (Checkbox)

### 💾 Data Management
- 🔄 **Auto-save** mọi thay đổi
- 📅 **Timestamps** cho created/modified
- 💿 **JSON storage** tại `~/.simple_notion_data.json`
- 📊 **Status bar** hiển thị trạng thái

## 🚀 Cách sử dụng

### Khởi động
```bash
# Cách 1: Script tiện lợi
./run_simple_notion.sh

# Cách 2: Chạy trực tiếp
python3 simple_notion.py
```

### Workflow cơ bản

1. **Tạo page đầu tiên**:
   - Click "New Page"
   - Nhập tên page trong dialog
   - Page sẽ được tạo và mở tự động

2. **Chỉnh sửa page**:
   - Click page trong sidebar để mở
   - Sửa title trong ô "Title"
   - Gõ nội dung trong text area
   - Sử dụng toolbar để format nhanh

3. **Quản lý pages**:
   - Tất cả pages hiển thị trong sidebar
   - Click để chuyển đổi giữa các pages
   - Click "Delete Page" để xóa

## 🎨 Giao diện

```
┌─────────────────────────────────────────────────────────┐
│ Simple Notion Clone                                     │
├─────────────────┬───────────────────────────────────────┤
│ Pages           │ Content                               │
│ ┌─────────────┐ │ Title: [Page Title____________]       │
│ │+ New Page   │ │                                       │
│ └─────────────┘ │ ┌─────────────────────────────────────┐ │
│                 │ │ Content area...                     │ │
│ • Page 1        │ │                                     │ │
│ • Page 2        │ │ Type your content here              │ │
│ • Page 3        │ │                                     │ │
│                 │ │                                     │ │
│                 │ └─────────────────────────────────────┘ │
│                 │ [H1][H2][List][Quote][Code][Todo][Del] │
│                 │ Status: Auto-saved - 2024-01-01 10:30 │
└─────────────────┴───────────────────────────────────────┘
```

## 🔧 Yêu cầu hệ thống

- **OS**: Ubuntu 18.04+ (hoặc Linux distros khác)
- **Python**: 3.6+
- **GUI**: tkinter (thường có sẵn)

### Cài đặt dependencies
```bash
sudo apt update
sudo apt install python3 python3-tk
```

## 💡 Tips sử dụng

### Markdown-style formatting
- Gõ `# ` để tạo heading 1
- Gõ `## ` để tạo heading 2
- Gõ `- ` để tạo bullet list
- Gõ `> ` để tạo quote
- Gõ `- [ ] ` để tạo todo item

### Keyboard shortcuts
- **Tab** - Indent trong text area
- **Ctrl+A** - Select all
- **Ctrl+C/V** - Copy/Paste
- **Ctrl+Z** - Undo (built-in tkinter)

## 📁 File structure

```
project/
├── simple_notion.py          # Main application
├── run_simple_notion.sh       # Launch script
├── README_SIMPLE_NOTION.md    # This file
└── ~/.simple_notion_data.json # Data file (auto-created)
```

## 🔍 Data format

```json
[
  {
    "id": "uuid-string",
    "title": "Page Title",
    "content": "Page content...",
    "created_date": "2024-01-01 10:30:00",
    "modified_date": "2024-01-01 11:45:00"
  }
]
```

## 🐛 Troubleshooting

### Lỗi "tkinter not found"
```bash
sudo apt install python3-tk
```

### Lỗi "Permission denied"
```bash
chmod +x run_simple_notion.sh
```

### App không lưu data
- Kiểm tra quyền ghi trong home directory
- File data sẽ tạo tại `~/.simple_notion_data.json`

## 🚧 Tính năng có thể thêm

- [ ] **Search**: Tìm kiếm trong pages
- [ ] **Export**: Xuất ra Markdown/PDF
- [ ] **Import**: Nhập từ file text
- [ ] **Themes**: Dark mode
- [ ] **Font size**: Tùy chỉnh font
- [ ] **Word count**: Đếm từ
- [ ] **Recent pages**: Pages gần đây

## 📄 License

MIT License - Sử dụng tự do.

---

**Enjoy your simple Notion experience! 🎉**

*Phiên bản này được tối ưu để tránh lỗi X11 và chạy ổn định trên mọi hệ thống Ubuntu.*
