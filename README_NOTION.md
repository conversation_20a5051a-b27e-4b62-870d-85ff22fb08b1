# 📝 Notion Clone - Ứng dụng ghi chú nâng cao

Ứng dụng ghi chú được thiết kế giống Notion với giao diện hiện đại và các tính năng nâng cao.

## ✨ Tính năng chính

### 🎨 Giao diện giống Notion
- **Sidebar** với danh sách pages
- **Main editor** với toolbar và formatting
- **Color scheme** giống Notion (trắng, xám nhẹ)
- **Typography** đẹp với nhiều font size

### 📄 Quản lý Pages
- ➕ Tạo page mới
- 📝 Chỉnh sửa title và content
- 🗑️ Xóa page
- 🔄 Auto-save (tự động lưu sau 1 giây)
- 📅 Hiển thị ngày tạo và sửa

### 🎯 Block-based Editor
- **Heading 1** (`# Text`)
- **Heading 2** (`## Text`)
- **Bullet List** (`• Item`)
- **Numbered List** (`1. Item`)
- **Quote** (`> Quote`)
- **Code Block** (```code```)
- **To-do** (`☐ Task`)

### ⌨️ Shortcuts và Commands

#### Slash Commands (/)
Gõ `/` để mở menu nhanh:
- `/text` - Text thường
- `/h1` - Heading 1
- `/h2` - Heading 2
- `/bullet` - Bullet list
- `/numbered` - Numbered list
- `/quote` - Quote block
- `/code` - Code block
- `/todo` - To-do item

#### Keyboard Shortcuts
- `Enter` - Tự động tiếp tục list
- `Ctrl+B` - Bold (placeholder)

#### Smart Lists
- Gõ `• ` rồi Enter → Tự động tạo bullet point tiếp theo
- Gõ `1. ` rồi Enter → Tự động tạo numbered list tiếp theo

## 🚀 Cách sử dụng

### Chạy ứng dụng
```bash
# Cách 1: Script tiện lợi
./run_notion.sh

# Cách 2: Chạy trực tiếp
python3 notion_app.py
```

### Workflow cơ bản

1. **Tạo page mới**:
   - Click "New Page" trong sidebar
   - Hoặc click "Create New Page" ở welcome screen

2. **Chỉnh sửa page**:
   - Click vào title để đổi tên
   - Gõ nội dung trong editor
   - Sử dụng toolbar hoặc slash commands

3. **Formatting nhanh**:
   - Gõ `/` để mở menu commands
   - Click các nút trong toolbar
   - Sử dụng markdown syntax

4. **Quản lý pages**:
   - Click page trong sidebar để mở
   - Click "Delete Page" để xóa
   - Pages được auto-save liên tục

## 🎨 Giao diện

```
┌─────────────────────────────────────────────────────────────┐
│ 📝 My Notion                    │ Page Title                │
│ + New Page                      │ ═══════════               │
│                                 │                           │
│ Pages                           │ [H1][H2][•][1.]["][</>]  │
│ ├─ 📄 Page 1                   │ ┌─────────────────────────┐ │
│ ├─ 📄 Page 2                   │ │ Content area            │ │
│ └─ 📄 Page 3                   │ │                         │ │
│                                 │ │ Type / for commands     │ │
│                                 │ │                         │ │
│                                 │ └─────────────────────────┘ │
│                                 │ Created: ... | [Delete]   │
└─────────────────────────────────────────────────────────────┘
```

## 💾 Lưu trữ dữ liệu

- Dữ liệu được lưu vào `~/.notion_app_data.json`
- Format JSON với các trường:
  - `id`: UUID duy nhất
  - `title`: Tiêu đề page
  - `content`: Nội dung
  - `created_date`: Ngày tạo
  - `modified_date`: Ngày sửa cuối

## 🔧 Yêu cầu hệ thống

- **OS**: Ubuntu 18.04+ (hoặc Linux distros khác)
- **Python**: 3.6+
- **Dependencies**: tkinter (có sẵn với Python)

## 🎯 Tính năng nâng cao

### Auto-save
- Tự động lưu sau 1 giây khi ngừng gõ
- Không cần Ctrl+S

### Smart Editing
- Tự động tiếp tục lists khi nhấn Enter
- Slash commands như Notion thật

### Responsive Design
- Sidebar có thể scroll
- Content area responsive
- Minimum window size

## 🚧 Tính năng sẽ thêm

- [ ] **Rich Text**: Bold, Italic, Underline
- [ ] **Tables**: Tạo và chỉnh sửa bảng
- [ ] **Images**: Chèn và hiển thị hình ảnh
- [ ] **Links**: Liên kết giữa các pages
- [ ] **Search**: Tìm kiếm trong tất cả pages
- [ ] **Export**: Xuất ra PDF, Markdown
- [ ] **Themes**: Dark mode, custom colors
- [ ] **Collaboration**: Chia sẻ và cộng tác

## 🐛 Báo lỗi

Nếu gặp lỗi, vui lòng tạo issue với thông tin:
- Hệ điều hành
- Phiên bản Python
- Mô tả lỗi chi tiết
- Steps to reproduce

## 📄 License

MIT License - Sử dụng tự do cho mục đích cá nhân và thương mại.

---

**Enjoy your Notion-like experience! 🎉**
