#!/usr/bin/env python3
"""
Setup script cho ứng dụng Notes App
"""

from setuptools import setup, find_packages
import os

# Đọc README
def read_readme():
    try:
        with open("README.md", "r", encoding="utf-8") as f:
            return f.read()
    except FileNotFoundError:
        return "Ứng dụng ghi chú với giao diện dạng bảng"

setup(
    name="notes-app",
    version="1.0.0",
    description="Ứng dụng ghi chú với giao diện dạng bảng",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    author="Assistant",
    author_email="<EMAIL>",
    url="https://github.com/example/notes-app",
    py_modules=["notes_app"],
    python_requires=">=3.6",
    install_requires=[
        # tkinter thường đã có sẵn trong Python
    ],
    entry_points={
        "console_scripts": [
            "notes-app=notes_app:main",
        ],
        "gui_scripts": [
            "notes-app-gui=notes_app:main",
        ],
    },
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: End Users/Desktop",
        "License :: OSI Approved :: MIT License",
        "Operating System :: POSIX :: Linux",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.6",
        "Programming Language :: Python :: 3.7",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Office/Business",
        "Topic :: Utilities",
    ],
    keywords="notes, notepad, gui, tkinter, ubuntu",
    project_urls={
        "Bug Reports": "https://github.com/example/notes-app/issues",
        "Source": "https://github.com/example/notes-app",
    },
)
